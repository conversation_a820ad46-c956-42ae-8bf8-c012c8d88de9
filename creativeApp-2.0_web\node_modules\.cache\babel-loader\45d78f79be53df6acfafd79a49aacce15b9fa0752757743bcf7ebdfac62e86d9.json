{"ast": null, "code": "// src/components/pages/passwordManager/PasswordCardsTable.js\nimport React,{useState,useEffect}from\"react\";import AddPasswordCardForm from\"./AddPasswordCardForm\";import{confirmationAlert}from\"../../common/coreui\";import FetchLoggedInRole from\"../../common/fetchData/FetchLoggedInRole\";import{useGetPasswordManagerDataQuery,useDeletePasswordManagerMutation,useUpdatePasswordManagerMutation}from\"../../features/api/passwordManagerApi\";// ADD THESE IMPORTS FOR DROPDOWN DATA\nimport{useGetDepartmentsWithTeamsQuery}from\"../../features/api/departmentApi\";import{useGetTeamsWithDepartmentsQuery}from\"../../features/api/teamApi\";import{toast}from\"sonner\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PasswordCardsTable=_ref=>{let{generatedPassword,passwordStrength}=_ref;// Get current user data\nconst{userData}=FetchLoggedInRole();const currentUserId=userData===null||userData===void 0?void 0:userData.id;// API hooks\nconst{data:dataItems,isLoading,refetch}=useGetPasswordManagerDataQuery({sort_by:\"created_at\",order:\"desc\",page:1,per_page:100,query:\"\"});// ADD THESE API HOOKS FOR DROPDOWN DATA\nconst{data:departmentsData}=useGetDepartmentsWithTeamsQuery();const{data:teamsData,isLoading:teamsLoading}=useGetTeamsWithDepartmentsQuery();console.log('departmentsData:',departmentsData);console.log('teamsData:',teamsData);const[deletePasswordManager]=useDeletePasswordManagerMutation();const[updatePasswordManager]=useUpdatePasswordManagerMutation();// State for managing multiple tables\nconst[tables,setTables]=useState([{id:1,name:\"Teams Password Card\"}]);// State for each table\nconst[tableStates,setTableStates]=useState({1:{showAddForm:false,editingRowId:null,visiblePasswords:{},shareableCards:[],editingHeader:false,headerTitle:\"Teams Password Card\"}});// State to track which passwords belong to which table\nconst[tablePasswordAssociations,setTablePasswordAssociations]=useState({});// FIXED: Load persisted tables and associations from localStorage on mount\nuseEffect(()=>{if(!currentUserId)return;const storageKey=`passwordTables_${currentUserId}`;const persistedData=localStorage.getItem(storageKey);if(persistedData){const{tables:persistedTables,associations:persistedAssociations}=JSON.parse(persistedData);setTables(persistedTables);setTablePasswordAssociations(persistedAssociations);// Initialize tableStates for loaded tables\nconst newTableStates={};persistedTables.forEach(table=>{newTableStates[table.id]=tableStates[table.id]||{showAddForm:false,editingRowId:null,visiblePasswords:{},shareableCards:[],editingHeader:false,headerTitle:table.name};});setTableStates(newTableStates);}},[currentUserId]);// FIXED: Save tables and associations to localStorage whenever they change\nuseEffect(()=>{if(!currentUserId)return;const storageKey=`passwordTables_${currentUserId}`;localStorage.setItem(storageKey,JSON.stringify({tables,associations:tablePasswordAssociations}));},[tables,tablePasswordAssociations,currentUserId]);// Initialize existing passwords ONLY if no persisted data\nconst[initializedExistingPasswords,setInitializedExistingPasswords]=useState(false);useEffect(()=>{if(!initializedExistingPasswords&&dataItems!==null&&dataItems!==void 0&&dataItems.data&&dataItems.data.length>0&&Object.keys(tablePasswordAssociations).length===0// Check if no persisted associations\n){const existingPasswordIds=dataItems.data.map(item=>item.id);setTablePasswordAssociations({1:existingPasswordIds});setInitializedExistingPasswords(true);}},[dataItems,initializedExistingPasswords,tablePasswordAssociations]);// --- NEW FUNCTION: Add a new empty table ---\nconst handleAddNewTable=()=>{// Find the next available ID for the new table\nconst newTableId=tables.length>0?Math.max(...tables.map(t=>t.id))+1:1;const newTableName=`Teams Password Card ${newTableId}`;// Or any default name\n// Add the new table to the tables list\nsetTables(prev=>[...prev,{id:newTableId,name:newTableName}]);// Initialize the state for the new table\nsetTableStates(prev=>({...prev,[newTableId]:{showAddForm:false,editingRowId:null,visiblePasswords:{},shareableCards:[],editingHeader:false,headerTitle:newTableName}}));// Initialize empty password association for the new table\nsetTablePasswordAssociations(prev=>({...prev,[newTableId]:[]}));toast.success(`New table \"${newTableName}\" created!`);};// Get state for specific table\nconst getTableState=tableId=>{return tableStates[tableId]||{showAddForm:false,editingRowId:null,visiblePasswords:{},shareableCards:[],editingHeader:false,headerTitle:`Teams Password Card ${tableId}`};};// Update state for specific table\nconst updateTableState=(tableId,updates)=>{setTableStates(prev=>({...prev,[tableId]:{...getTableState(tableId),...updates}}));};// Get passwords for a specific table\nconst getTableData=tableId=>{const allData=(dataItems===null||dataItems===void 0?void 0:dataItems.data)||[];const tablePasswords=tablePasswordAssociations[tableId]||[];return allData.filter(item=>tablePasswords.includes(item.id));};// Associate a password with a table\nconst associatePasswordWithTable=(tableId,passwordId)=>{setTablePasswordAssociations(prev=>({...prev,[tableId]:[...(prev[tableId]||[]),passwordId]}));};// Remove password association from table\nconst removePasswordFromTable=(tableId,passwordId)=>{setTablePasswordAssociations(prev=>({...prev,[tableId]:(prev[tableId]||[]).filter(id=>id!==passwordId)}));};// Get team members for avatar display - FIXED: Proper backend integration\nconst getTeamMembers=()=>{const members=[];// Add current user first\nif(userData){members.push({id:userData.id,fname:userData.fname||\"User\",lname:userData.lname||\"\",photo:userData.photo||null});}// FIXED: Add team members from backend data with proper structure\nif(userData!==null&&userData!==void 0&&userData.teams&&userData.teams.length>0){userData.teams.forEach(team=>{// Check if team has users (members) loaded\nif(team.users&&Array.isArray(team.users)){team.users.forEach(member=>{// Avoid duplicate current user\nif(member.id!==userData.id){members.push({id:member.id,fname:member.fname||\"Team\",lname:member.lname||\"Member\",photo:member.photo||null});}});}});}// If no team members found, add some placeholder members for demo\nif(members.length===1){for(let i=2;i<=4;i++){members.push({id:`placeholder_${i}`,fname:`Team`,lname:`Member ${i}`,photo:null});}}return members.slice(0,4);// Show max 4 avatars\n};// Toggle password visibility for specific table\nconst togglePasswordVisibility=(tableId,cardId)=>{const currentState=getTableState(tableId);updateTableState(tableId,{visiblePasswords:{...currentState.visiblePasswords,[cardId]:!currentState.visiblePasswords[cardId]}});};// Handle edit for specific table\nconst handleEdit=(tableId,cardId)=>{const currentState=getTableState(tableId);if(currentState.editingRowId===cardId){// Exit edit mode\nupdateTableState(tableId,{editingRowId:null});}else{// Enter edit mode\nupdateTableState(tableId,{editingRowId:cardId});}};// UPDATED: Handle inline edit save with proper dropdown support\nconst handleInlineEditSave=async(tableId,cardId,field,value)=>{try{var _dataItems$data;const item=dataItems===null||dataItems===void 0?void 0:(_dataItems$data=dataItems.data)===null||_dataItems$data===void 0?void 0:_dataItems$data.find(item=>item.id===cardId);if(!item){toast.error(\"Item not found\");return;}// UPDATED: Proper data structure for backend with dropdown support\nlet updateData={password_title:item.password_title||item.title,username:item.username,password:item.password,department_id:item.department_id,team_id:item.team_id,user_id:item.user_id||currentUserId// Always include user_id for backend validation\n};// UPDATED: Handle different field updates including dropdown selections\nif(field===\"title\"){updateData.password_title=value;}else if(field===\"username\"){updateData.username=value;}else if(field===\"password\"){// Handle password update\nupdateData.password=value;}else if(field===\"team\"){// Handle team dropdown selection - value should be team ID\nupdateData.team_id=value?parseInt(value):null;}else if(field===\"department\"){// Handle department dropdown selection - value should be department ID\nupdateData.department_id=value?parseInt(value):null;// Reset team when department changes\nupdateData.team_id=null;}console.log(\"Updating with data:\",updateData);// Debug log\nawait updatePasswordManager({id:cardId,...updateData}).unwrap();// FIXED: Await refetch to ensure data is refreshed before proceeding\nawait refetch();// FIXED: Auto-exit editing mode after successful save\nupdateTableState(tableId,{editingRowId:null});toast.success(\"Successfully updated!\");}catch(error){var _error$data,_error$data$errors,_error$data$errors$pa;console.error(\"Error updating:\",error);// Improved error handling for duplicate title\nif(error!==null&&error!==void 0&&(_error$data=error.data)!==null&&_error$data!==void 0&&(_error$data$errors=_error$data.errors)!==null&&_error$data$errors!==void 0&&(_error$data$errors$pa=_error$data$errors.password_title)!==null&&_error$data$errors$pa!==void 0&&_error$data$errors$pa[0]){toast.error(error.data.errors.password_title[0]);}else{var _error$data2;toast.error((error===null||error===void 0?void 0:(_error$data2=error.data)===null||_error$data2===void 0?void 0:_error$data2.message)||\"Failed to update. Please try again.\");}}};// Handle delete\nconst handleDelete=async cardId=>{confirmationAlert({onConfirm:async()=>{try{await deletePasswordManager(cardId).unwrap();// Remove from all table associations\nObject.keys(tablePasswordAssociations).forEach(tableId=>{removePasswordFromTable(parseInt(tableId),cardId);});refetch();toast.success(\"Password deleted successfully!\");}catch(error){console.error(\"Error deleting password:\",error);toast.error(\"Failed to delete password. Please try again.\");}}});};// Handle successful password creation/update - FIXED: Reset form properly\nconst handlePasswordSuccess=function(tableId){let newPasswordId=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;refetch().then(()=>{// If a new password was created, associate it with the table\nif(newPasswordId){associatePasswordWithTable(tableId,newPasswordId);}});updateTableState(tableId,{showAddForm:false,editingRowId:null});// FIXED: Only one success notification, no duplicate\ntoast.success(\"Password saved successfully!\");};// Toggle shareable card selection\nconst toggleShareableCard=(tableId,cardId)=>{const currentState=getTableState(tableId);const currentShareable=currentState.shareableCards;const newShareable=currentShareable.includes(cardId)?currentShareable.filter(id=>id!==cardId):[...currentShareable,cardId];updateTableState(tableId,{shareableCards:newShareable});};// Toggle all shareable cards\nconst toggleAllShareable=tableId=>{const currentState=getTableState(tableId);const tableData=(dataItems===null||dataItems===void 0?void 0:dataItems.data)||[];if(currentState.shareableCards.length===tableData.length){updateTableState(tableId,{shareableCards:[]});}else{updateTableState(tableId,{shareableCards:tableData.map(card=>card.id)});}};// FIXED: Handle share - Now copies selected rows to clipboard\nconst handleShare=async tableId=>{const currentState=getTableState(tableId);const tableData=getTableData(tableId);const selectedCards=tableData.filter(card=>currentState.shareableCards.includes(card.id));if(selectedCards.length===0){toast.error(\"Please select at least one row to copy.\");return;}// Format the selected rows as a string (e.g., \"Title: title, Username: username, Password: password\")\nconst copiedText=selectedCards.map(card=>`Title: ${card.password_title||card.title}, `+`Username: ${card.username}, `+`Password: ${card.password}`).join(\"\\n\");// Copy to clipboard\ntry{await navigator.clipboard.writeText(copiedText);toast.success(`Successfully copied ${selectedCards.length} row(s) to clipboard!`);// Clear selections after copy\nupdateTableState(tableId,{shareableCards:[]});}catch(error){console.error(\"Error copying to clipboard:\",error);toast.error(\"Failed to copy rows. Please try again.\");}};// --- UPDATED FUNCTION: Handle delete entire table entries or selected entries ---\nconst handleDeleteTableEntries=async tableId=>{const currentState=getTableState(tableId);const tableData=getTableData(tableId);// Gets passwords associated with this table\n// FIXED: Special handling for empty tables - Delete the entire table directly\nif(tableData.length===0){if(tables.length<=1){toast.error(\"Cannot delete the last table. At least one table must remain.\");return;}confirmationAlert({title:\"Delete Empty Table?\",text:`Are you sure you want to delete the empty table \"${getTableState(tableId).headerTitle}\"? This action cannot be undone.`,onConfirm:async()=>{try{var _getTableState;// Remove the table from state and associations (no backend deletions needed since empty)\nsetTables(prev=>prev.filter(table=>table.id!==tableId));setTableStates(prev=>{const newState={...prev};delete newState[tableId];return newState;});setTablePasswordAssociations(prev=>{const newState={...prev};delete newState[tableId];return newState;});// Refetch (though not strictly needed for empty table)\nawait refetch();toast.success(`Empty table \"${((_getTableState=getTableState(tableId))===null||_getTableState===void 0?void 0:_getTableState.headerTitle)||'Unknown'}\" deleted successfully.`);}catch(error){console.error(\"Error deleting empty table:\",error);toast.error(\"Failed to delete table. Please try again.\");}}});return;}// For non-empty tables: Existing logic\nif(currentState.shareableCards.length===0){toast.error(\"Please select at least one row to delete.\");return;}const userOwnedCards=currentState.shareableCards.filter(cardId=>{const card=tableData.find(c=>c.id===cardId);return card&&card.user_id===currentUserId;});if(userOwnedCards.length===0){toast.error(\"You can only delete password entries that you created.\");return;}// FIXED: Special logic for secondary tables (id > 1)\nconst isSecondaryTable=tableId>1;const allEntriesSelected=tableData.length>0&&currentState.shareableCards.length===tableData.length;if(isSecondaryTable||allEntriesSelected){// For secondary tables (any selection) or primary table (all selected): Delete entire table\nif(tables.length<=1){// Prevent deletion if it's the last table\n// Clear associations instead\nsetTablePasswordAssociations(prev=>({...prev,[tableId]:[]// Clear password associations for this table\n}));updateTableState(tableId,{shareableCards:[]});// Clear selection\nawait refetch();toast.success(`All entries cleared from the last table.`);return;}confirmationAlert({title:\"Delete Entire Table?\",text:`Are you sure you want to delete the entire table \"${getTableState(tableId).headerTitle}\" and all its entries? This action cannot be undone.`,onConfirm:async()=>{try{var _getTableState2;// 1. Delete user-owned password entries from backend\nconst tableData=getTableData(tableId);const userOwnedCardIds=tableData.filter(card=>card.user_id===currentUserId).map(card=>card.id);if(userOwnedCardIds.length>0){await Promise.all(userOwnedCardIds.map(cardId=>deletePasswordManager(cardId).unwrap()));}// 2. Remove the table from state and associations\nsetTables(prev=>prev.filter(table=>table.id!==tableId));setTableStates(prev=>{const newState={...prev};delete newState[tableId];return newState;});setTablePasswordAssociations(prev=>{const newState={...prev};delete newState[tableId];return newState;});// 3. Refetch data\nawait refetch();toast.success(`Table \"${((_getTableState2=getTableState(tableId))===null||_getTableState2===void 0?void 0:_getTableState2.headerTitle)||'Unknown'}\" and its entries deleted successfully.`);}catch(error){console.error(\"Error deleting table or entries:\",error);toast.error(\"Failed to delete table or some entries. Please try again.\");}}});return;}// For primary table (partial selection): Delete only selected entries\nconfirmationAlert({onConfirm:async()=>{try{await Promise.all(userOwnedCards.map(cardId=>deletePasswordManager(cardId).unwrap()));// Remove from table associations\nuserOwnedCards.forEach(cardId=>{removePasswordFromTable(tableId,cardId);});updateTableState(tableId,{shareableCards:[]});await refetch();toast.success(`Successfully deleted ${userOwnedCards.length} password entries from this table.`);}catch(error){console.error(\"Error deleting password entries:\",error);toast.error(\"Failed to delete some password entries. Please try again.\");}}});};if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"})});}return/*#__PURE__*/_jsx(\"div\",{className:\"bg-white dark:bg-gray-900 px-4 sm:px-6 lg:px-8\",children:tables.map(table=>{const tableState=getTableState(table.id);const teamMembers=getTeamMembers();const tableData=getTableData(table.id);return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6 sm:mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6 gap-4 sm:gap-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 w-full sm:w-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto\",children:[tableState.editingHeader?/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:tableState.headerTitle,onChange:e=>updateTableState(table.id,{headerTitle:e.target.value}),onBlur:()=>updateTableState(table.id,{editingHeader:false}),onKeyDown:e=>{if(e.key===\"Enter\"){updateTableState(table.id,{editingHeader:false});}},className:\"w-full sm:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",autoFocus:true}):/*#__PURE__*/_jsx(\"h2\",{className:\"text-base sm:text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors\",onClick:()=>updateTableState(table.id,{editingHeader:true}),title:\"Click to edit table name\",children:tableState.headerTitle}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 sm:space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleShare(table.id),className:\"flex items-center justify-center py-1 px-2 sm:px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs sm:text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",title:\"Copy selected rows to clipboard\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"share\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteTableEntries(table.id),className:\"flex items-center justify-center py-1 px-2 sm:px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-xs sm:text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",title:\"Delete selected entries or table\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center -space-x-2 ml-0 sm:ml-2\",children:[teamMembers.slice(0,3).map((member,index)=>{var _member$fname,_member$lname;return/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[member!==null&&member!==void 0&&member.photo?/*#__PURE__*/_jsx(\"img\",{src:member!==null&&member!==void 0&&member.photo?`${process.env.REACT_APP_BASE_STORAGE_URL}/${member.photo}`:\"/default-avatar.png\",alt:`${member.fname||\"User\"} ${member.lname||\"\"}`,className:\"w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover border-2 border-white shadow-sm\",onError:e=>{e.target.style.display=\"none\";e.target.nextSibling.style.display=\"flex\";}}):null,/*#__PURE__*/_jsxs(\"div\",{className:\"w-6 h-6 sm:w-8 sm:h-8 bg-primary rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm\",style:{display:member!==null&&member!==void 0&&member.photo?\"none\":\"flex\"},children:[(member===null||member===void 0?void 0:(_member$fname=member.fname)===null||_member$fname===void 0?void 0:_member$fname.charAt(0))||\"T\",(member===null||member===void 0?void 0:(_member$lname=member.lname)===null||_member$lname===void 0?void 0:_member$lname.charAt(0))||\"M\"]})]},index);}),teamMembers.length>3&&/*#__PURE__*/_jsxs(\"div\",{className:\"w-6 h-6 sm:w-8 sm:h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white shadow-sm\",children:[\"+\",teamMembers.length-3]})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{updateTableState(table.id,{editingRowId:null,showAddForm:!tableState.showAddForm});},className:\"flex items-center justify-center py-2 px-3 sm:px-4 text-xs sm:text-sm font-medium bg-transparent text-black border-2 border-[#0B333F] rounded-full hover:bg-primary hover:text-white transition duration-500 ease-in-out focus:outline-none focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 shadow-sm w-full sm:w-auto sm:min-w-[200px] h-[36px] sm:h-[40px]\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4 sm:w-6 sm:h-6 mr-1 sm:mr-2 -ml-2 sm:-ml-3 flex items-center justify-center rounded-full bg-white border-2 border-black\",fill:\"none\",stroke:\"black\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M12 6v6m0 0v6m0-6h6m-6 0H6\"})}),\"Add Password\"]})]}),tableState.showAddForm&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200\",children:/*#__PURE__*/_jsx(AddPasswordCardForm,{onCancel:()=>{updateTableState(table.id,{showAddForm:false,editingRowId:null});},onSuccess:newPasswordId=>handlePasswordSuccess(table.id,newPasswordId),generatedPassword:generatedPassword,passwordStrength:passwordStrength,editData:tableState.editingRowId?tableData.find(item=>item.id===tableState.editingRowId):null,departmentsData:departmentsData,teamsData:teamsData})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg border border-gray-200 overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:tableData.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8 sm:py-12 px-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 mb-4 text-sm sm:text-base\",children:\"No password cards added yet. Click \\\"Add Password\\\" to add your first card.\"})}):/*#__PURE__*/_jsxs(\"table\",{className:\"w-full text-xs sm:text-sm text-left min-w-[800px]\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50 border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 w-8 sm:w-12\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:tableState.shareableCards.length===tableData.length&&tableData.length>0,onChange:()=>toggleAllShareable(table.id),className:\"w-3 h-3 sm:w-4 sm:h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"})}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[\"Title\",/*#__PURE__*/_jsx(\"svg\",{className:\"w-3 h-3 ml-1 text-gray-400\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",clipRule:\"evenodd\"})})]})}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[\"User Name\",/*#__PURE__*/_jsx(\"svg\",{className:\"w-3 h-3 ml-1 text-gray-400\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",clipRule:\"evenodd\"})})]})}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]\",children:\"Password\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px] sm:min-w-[120px]\",children:\"Team\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px] sm:min-w-[120px]\",children:\"Department\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px] sm:min-w-[100px]\",children:\"Level\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px] sm:min-w-[100px]\",children:\"Action\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:tableData.map(card=>/*#__PURE__*/_jsx(TableRow,{card:card,tableId:table.id,isEditing:tableState.editingRowId===card.id,isShareable:tableState.shareableCards.includes(card.id),visiblePassword:tableState.visiblePasswords[card.id],currentUserId:currentUserId// PASS CORRECT DROPDOWN DATA TO TableRow\n,departmentsData:departmentsData,teamsData:teamsData,onEdit:handleEdit,onDelete:handleDelete,onTogglePassword:togglePasswordVisibility,onToggleShareable:toggleShareableCard,onInlineEditSave:handleInlineEditSave},card.id))})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center mt-4 sm:mt-6\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddNewTable,className:\"flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 bg-[#006D94] text-white rounded-lg shadow-md hover:bg-[#005F80] transition duration-200 text-sm sm:text-base w-full sm:w-auto max-w-xs sm:max-w-none\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center border-2 border-white rounded-full flex-shrink-0\",children:/*#__PURE__*/_jsx(\"span\",{className:\"relative -top-[1px] sm:-top-[2px] text-base sm:text-lg font-thin\",children:\"+\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs sm:text-sm font-medium\",children:\"Add New Password Card\"})]})})]},table.id);})});};// UPDATED RESPONSIVE TableRow component\nconst TableRow=_ref2=>{var _ref3,_ref3$charAt,_card$team,_card$department;let{card,tableId,isEditing,isShareable,visiblePassword,currentUserId,departmentsData=[],teamsData=[],onEdit,onDelete,onTogglePassword,onToggleShareable,onInlineEditSave}=_ref2;// --- UPDATED: Proper initialization of edit values including password ---\nconst[editValues,setEditValues]=useState({title:card.password_title||card.title||card.user_name||\"\",username:card.username||card.user_name||\"\",password:card.password||\"\",team_id:card.team_id?String(card.team_id):\"\",department_id:card.department_id?String(card.department_id):\"\"});// FIXED: Sync editValues with updated card props after refetch\nuseEffect(()=>{setEditValues({title:card.password_title||card.title||card.user_name||\"\",username:card.username||card.user_name||\"\",password:card.password||\"\",team_id:card.team_id?String(card.team_id):\"\",department_id:card.department_id?String(card.department_id):\"\"});},[card]);// --- UPDATED: Handle input changes with department/team relationship logic and password ---\nconst handleInputChange=(field,value)=>{if(field===\"department_id\"){setEditValues(prev=>({...prev,[field]:value,team_id:\"\"}));}else{setEditValues(prev=>({...prev,[field]:value}));}};// --- UPDATED: Proper field value comparison and update for all fields including password ---\nconst handleInputBlur=field=>{let originalValue;if(field===\"title\"){originalValue=card.password_title||card.title||card.user_name;}else if(field===\"username\"){originalValue=card.username||card.user_name;}else if(field===\"password\"){originalValue=card.password;}else if(field===\"team_id\"){originalValue=card.team_id?String(card.team_id):\"\";}else if(field===\"department_id\"){originalValue=card.department_id?String(card.department_id):\"\";}// Client-side validation for required fields\nconst requiredFields=[\"title\",\"username\",\"password\",\"department_id\",\"team_id\"];if(requiredFields.includes(field)&&!editValues[field]){toast.error(`The ${field.replace('_',' ')} field is required.`);return;}if(editValues[field]!==originalValue){const backendField=field===\"team_id\"?\"team\":field===\"department_id\"?\"department\":field;onInlineEditSave(tableId,card.id,backendField,editValues[field]);}};const handleKeyDown=e=>{if(e.key===\"Enter\"){e.target.blur();}};// Get password strength styling\nconst getPasswordStrengthStyle=level=>{switch(level){case\"Strong\":return\"bg-green/10 text-[#22C55E]\";case\"Moderate\":return\"bg-yellow/10 text-[#F59E0B]\";case\"Weak\":return\"bg-red/10 text-[#EF4444]\";default:return\"bg-green-100 text-green-700 border border-green-200\";}};// UPDATED: Get filtered teams based on selected department\nconst getFilteredTeams=()=>{if(!editValues.department_id||!Array.isArray(teamsData))return[];return teamsData.filter(team=>team.department_ids&&team.department_ids.includes(parseInt(editValues.department_id)));};return/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50 transition-colors\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isShareable,onChange:()=>onToggleShareable(tableId,card.id),className:\"w-3 h-3 sm:w-4 sm:h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 sm:w-10 sm:h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-2 sm:mr-3 text-xs sm:text-sm flex-shrink-0\",children:((_ref3=card.password_title||card.title||card.user_name)===null||_ref3===void 0?void 0:(_ref3$charAt=_ref3.charAt(0))===null||_ref3$charAt===void 0?void 0:_ref3$charAt.toUpperCase())||\"P\"}),isEditing?/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:editValues.title,onChange:e=>handleInputChange(\"title\",e.target.value),onBlur:()=>handleInputBlur(\"title\"),onKeyDown:handleKeyDown,className:\"font-medium text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-primary focus:border-blue-500 text-xs sm:text-sm\",autoFocus:true,placeholder:\"Platform Title\"}):/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-900 text-xs sm:text-sm break-words\",children:card.password_title||card.title||card.user_name})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[isEditing?/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:editValues.username,onChange:e=>handleInputChange(\"username\",e.target.value),onBlur:()=>handleInputBlur(\"username\"),onKeyDown:handleKeyDown,className:\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-1 sm:mr-2 focus:ring-2 focus:ring-primary focus:border-blue-500 text-xs sm:text-sm\",placeholder:\"Username or Email\"}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 mr-1 sm:mr-2 text-xs sm:text-sm break-all\",children:card.username||card.user_name}),/*#__PURE__*/_jsx(\"button\",{className:\"flex items-center justify-center py-1 px-1 sm:px-2 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 flex-shrink-0\",title:\"Copy username\",onClick:()=>{const username=card.username||card.user_name;if(username){navigator.clipboard.writeText(username);toast.success(\"Username copied to clipboard!\");}},children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"content_copy\"})})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[isEditing?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"input\",{type:visiblePassword?\"text\":\"password\",value:editValues.password,onChange:e=>handleInputChange(\"password\",e.target.value),onBlur:()=>handleInputBlur(\"password\"),onKeyDown:handleKeyDown,className:\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-1 focus:ring-2 focus:ring-primary focus:border-blue-500 font-mono text-xs sm:text-sm\",placeholder:\"Password\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onTogglePassword(tableId,card.id),className:\"flex items-center justify-center py-1 px-1 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 mr-1 flex-shrink-0\",title:visiblePassword?\"Hide password\":\"Show password\",type:\"button\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:visiblePassword?\"visibility_off\":\"visibility\"})})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 mr-1 font-mono text-xs sm:text-sm break-all\",children:visiblePassword?card.password:\"••••••••••••\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onTogglePassword(tableId,card.id),className:\"flex items-center justify-center py-1 px-1 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 mr-1 flex-shrink-0\",title:visiblePassword?\"Hide password\":\"Show password\",type:\"button\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:visiblePassword?\"visibility_off\":\"visibility\"})})]}),/*#__PURE__*/_jsx(\"button\",{className:\"flex items-center justify-center py-1 px-1 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 flex-shrink-0\",title:\"Copy password\",onClick:()=>{const passwordToCopy=isEditing?editValues.password:card.password;if(passwordToCopy){navigator.clipboard.writeText(passwordToCopy);toast.success(\"Password copied to clipboard!\");}},type:\"button\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"content_copy\"})})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:isEditing?/*#__PURE__*/_jsxs(\"select\",{value:editValues.team_id,onChange:e=>handleInputChange(\"team_id\",e.target.value),onBlur:()=>handleInputBlur(\"team_id\"),disabled:!editValues.department_id,className:`text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm ${!editValues.department_id?\"bg-gray-100 cursor-not-allowed\":\"\"}`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Team\"}),getFilteredTeams().map(team=>/*#__PURE__*/_jsx(\"option\",{value:team.id,children:team.name},team.id))]}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 text-xs sm:text-sm break-words\",children:((_card$team=card.team)===null||_card$team===void 0?void 0:_card$team.name)||\"Team Name\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:isEditing?/*#__PURE__*/_jsxs(\"select\",{value:editValues.department_id||\"\",onChange:e=>handleInputChange(\"department_id\",e.target.value),onBlur:()=>handleInputBlur(\"department_id\"),className:\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Department\"}),departmentsData.map(dept=>/*#__PURE__*/_jsx(\"option\",{value:dept.id,children:dept.name},dept.id))]}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 text-xs sm:text-sm break-words\",children:((_card$department=card.department)===null||_card$department===void 0?void 0:_card$department.name)||\"Department name\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 text-xs font-medium rounded ${getPasswordStrengthStyle(card.level)}`,children:card.level||\"Strong Password\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-2 sm:px-4 py-3 sm:py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(tableId,card.id),className:\"flex items-center justify-center py-1 px-1 sm:px-2 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200\",title:\"Edit\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"stylus_note\"})}),card.user_id===currentUserId&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(card.id),className:\"flex items-center justify-center py-1 px-1 sm:px-2 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200\",title:\"Delete\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined text-sm\",children:\"delete\"})})]})})]});};export default PasswordCardsTable;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AddPasswordCardForm", "<PERSON><PERSON><PERSON><PERSON>", "FetchLoggedInRole", "useGetPasswordManagerDataQuery", "useDeletePasswordManagerMutation", "useUpdatePasswordManagerMutation", "useGetDepartmentsWithTeamsQuery", "useGetTeamsWithDepartmentsQuery", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PasswordCardsTable", "_ref", "generatedPassword", "passwordStrength", "userData", "currentUserId", "id", "data", "dataItems", "isLoading", "refetch", "sort_by", "order", "page", "per_page", "query", "departmentsData", "teamsData", "teamsLoading", "console", "log", "deletePasswordManager", "updatePasswordManager", "tables", "setTables", "name", "tableStates", "setTableStates", "showAddForm", "editingRowId", "visiblePasswords", "shareableCards", "<PERSON><PERSON><PERSON><PERSON>", "headerTitle", "tablePasswordAssociations", "setTablePasswordAssociations", "storageKey", "persistedData", "localStorage", "getItem", "persistedTables", "associations", "persistedAssociations", "JSON", "parse", "newTableStates", "for<PERSON>ach", "table", "setItem", "stringify", "initializedExistingPasswords", "setInitializedExistingPasswords", "length", "Object", "keys", "existingPasswordIds", "map", "item", "handleAddNewTable", "newTableId", "Math", "max", "t", "newTableName", "prev", "success", "getTableState", "tableId", "updateTableState", "updates", "getTableData", "allData", "tablePasswords", "filter", "includes", "associatePasswordWithTable", "passwordId", "removePasswordFromTable", "getTeamMembers", "members", "push", "fname", "lname", "photo", "teams", "team", "users", "Array", "isArray", "member", "i", "slice", "togglePasswordVisibility", "cardId", "currentState", "handleEdit", "handleInlineEditSave", "field", "value", "_dataItems$data", "find", "error", "updateData", "password_title", "title", "username", "password", "department_id", "team_id", "user_id", "parseInt", "unwrap", "_error$data", "_error$data$errors", "_error$data$errors$pa", "errors", "_error$data2", "message", "handleDelete", "onConfirm", "handlePasswordSuccess", "newPasswordId", "arguments", "undefined", "then", "toggleShareableCard", "currentShareable", "newShareable", "toggleAllShareable", "tableData", "card", "handleShare", "selectedCards", "copiedText", "join", "navigator", "clipboard", "writeText", "handleDeleteTableEntries", "text", "_getTableState", "newState", "userOwnedCards", "c", "isSecondaryTable", "allEntriesSelected", "_getTableState2", "userOwnedCardIds", "Promise", "all", "className", "children", "tableState", "teamMembers", "type", "onChange", "e", "target", "onBlur", "onKeyDown", "key", "autoFocus", "onClick", "index", "_member$fname", "_member$lname", "src", "process", "env", "REACT_APP_BASE_STORAGE_URL", "alt", "onError", "style", "display", "nextS<PERSON>ling", "char<PERSON>t", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onCancel", "onSuccess", "editData", "scope", "checked", "fillRule", "clipRule", "TableRow", "isEditing", "isShareable", "visiblePassword", "onEdit", "onDelete", "onTogglePassword", "onToggleShareable", "onInlineEditSave", "_ref2", "_ref3", "_ref3$charAt", "_card$team", "_card$department", "edit<PERSON><PERSON><PERSON>", "setEditV<PERSON>ues", "user_name", "String", "handleInputChange", "handleInputBlur", "originalValue", "requiredFields", "replace", "backendField", "handleKeyDown", "blur", "getPasswordStrengthStyle", "level", "getFilteredTeams", "department_ids", "toUpperCase", "placeholder", "passwordToCopy", "disabled", "dept", "department"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["// src/components/pages/passwordManager/PasswordCardsTable.js\r\nimport React, { useState, useEffect } from \"react\";\r\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\r\nimport { confirmationAlert } from \"../../common/coreui\";\r\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\r\nimport {\r\n  useGetPasswordManagerDataQuery,\r\n  useDeletePasswordManagerMutation,\r\n  useUpdatePasswordManagerMutation,\r\n} from \"../../features/api/passwordManagerApi\";\r\n// ADD THESE IMPORTS FOR DROPDOWN DATA\r\nimport { useGetDepartmentsWithTeamsQuery } from \"../../features/api/departmentApi\";\r\nimport { useGetTeamsWithDepartmentsQuery } from \"../../features/api/teamApi\";\r\nimport { toast } from \"sonner\";\r\n\r\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\r\n  // Get current user data\r\n  const { userData } = FetchLoggedInRole();\r\n  const currentUserId = userData?.id;\r\n  // API hooks\r\n  const {\r\n    data: dataItems,\r\n    isLoading,\r\n    refetch,\r\n  } = useGetPasswordManagerDataQuery({\r\n    sort_by: \"created_at\",\r\n    order: \"desc\",\r\n    page: 1,\r\n    per_page: 100,\r\n    query: \"\",\r\n  });\r\n  // ADD THESE API HOOKS FOR DROPDOWN DATA\r\n  const { data: departmentsData } = useGetDepartmentsWithTeamsQuery();\r\n  const { data: teamsData, isLoading: teamsLoading } = useGetTeamsWithDepartmentsQuery();\r\n  console.log('departmentsData:', departmentsData);\r\n  console.log('teamsData:', teamsData);\r\n  const [deletePasswordManager] = useDeletePasswordManagerMutation();\r\n  const [updatePasswordManager] = useUpdatePasswordManagerMutation();\r\n\r\n  // State for managing multiple tables\r\n  const [tables, setTables] = useState([{ id: 1, name: \"Teams Password Card\" }]);\r\n\r\n  // State for each table\r\n  const [tableStates, setTableStates] = useState({\r\n    1: {\r\n      showAddForm: false,\r\n      editingRowId: null,\r\n      visiblePasswords: {},\r\n      shareableCards: [],\r\n      editingHeader: false,\r\n      headerTitle: \"Teams Password Card\",\r\n    },\r\n  });\r\n\r\n  // State to track which passwords belong to which table\r\n  const [tablePasswordAssociations, setTablePasswordAssociations] = useState({});\r\n\r\n  // FIXED: Load persisted tables and associations from localStorage on mount\r\n  useEffect(() => {\r\n    if (!currentUserId) return;\r\n    const storageKey = `passwordTables_${currentUserId}`;\r\n    const persistedData = localStorage.getItem(storageKey);\r\n    if (persistedData) {\r\n      const { tables: persistedTables, associations: persistedAssociations } = JSON.parse(persistedData);\r\n      setTables(persistedTables);\r\n      setTablePasswordAssociations(persistedAssociations);\r\n      // Initialize tableStates for loaded tables\r\n      const newTableStates = {};\r\n      persistedTables.forEach(table => {\r\n        newTableStates[table.id] = tableStates[table.id] || {\r\n          showAddForm: false,\r\n          editingRowId: null,\r\n          visiblePasswords: {},\r\n          shareableCards: [],\r\n          editingHeader: false,\r\n          headerTitle: table.name,\r\n        };\r\n      });\r\n      setTableStates(newTableStates);\r\n    }\r\n  }, [currentUserId]);\r\n\r\n  // FIXED: Save tables and associations to localStorage whenever they change\r\n  useEffect(() => {\r\n    if (!currentUserId) return;\r\n    const storageKey = `passwordTables_${currentUserId}`;\r\n    localStorage.setItem(storageKey, JSON.stringify({\r\n      tables,\r\n      associations: tablePasswordAssociations,\r\n    }));\r\n  }, [tables, tablePasswordAssociations, currentUserId]);\r\n\r\n  // Initialize existing passwords ONLY if no persisted data\r\n  const [initializedExistingPasswords, setInitializedExistingPasswords] = useState(false);\r\n  useEffect(() => {\r\n    if (\r\n      !initializedExistingPasswords &&\r\n      dataItems?.data &&\r\n      dataItems.data.length > 0 &&\r\n      Object.keys(tablePasswordAssociations).length === 0 // Check if no persisted associations\r\n    ) {\r\n      const existingPasswordIds = dataItems.data.map((item) => item.id);\r\n      setTablePasswordAssociations({ 1: existingPasswordIds });\r\n      setInitializedExistingPasswords(true);\r\n    }\r\n  }, [dataItems, initializedExistingPasswords, tablePasswordAssociations]);\r\n\r\n  // --- NEW FUNCTION: Add a new empty table ---\r\n  const handleAddNewTable = () => {\r\n    // Find the next available ID for the new table\r\n    const newTableId = tables.length > 0 ? Math.max(...tables.map(t => t.id)) + 1 : 1;\r\n    const newTableName = `Teams Password Card ${newTableId}`; // Or any default name\r\n\r\n    // Add the new table to the tables list\r\n    setTables((prev) => [...prev, { id: newTableId, name: newTableName }]);\r\n\r\n    // Initialize the state for the new table\r\n    setTableStates((prev) => ({\r\n      ...prev,\r\n      [newTableId]: {\r\n        showAddForm: false,\r\n        editingRowId: null,\r\n        visiblePasswords: {},\r\n        shareableCards: [],\r\n        editingHeader: false,\r\n        headerTitle: newTableName,\r\n      },\r\n    }));\r\n\r\n    // Initialize empty password association for the new table\r\n    setTablePasswordAssociations((prev) => ({\r\n      ...prev,\r\n      [newTableId]: [],\r\n    }));\r\n\r\n    toast.success(`New table \"${newTableName}\" created!`);\r\n  };\r\n\r\n  // Get state for specific table\r\n  const getTableState = (tableId) => {\r\n    return (\r\n      tableStates[tableId] || {\r\n        showAddForm: false,\r\n        editingRowId: null,\r\n        visiblePasswords: {},\r\n        shareableCards: [],\r\n        editingHeader: false,\r\n        headerTitle: `Teams Password Card ${tableId}`,\r\n      }\r\n    );\r\n  };\r\n\r\n  // Update state for specific table\r\n  const updateTableState = (tableId, updates) => {\r\n    setTableStates((prev) => ({\r\n      ...prev,\r\n      [tableId]: {\r\n        ...getTableState(tableId),\r\n        ...updates,\r\n      },\r\n    }));\r\n  };\r\n\r\n  // Get passwords for a specific table\r\n  const getTableData = (tableId) => {\r\n    const allData = dataItems?.data || [];\r\n    const tablePasswords = tablePasswordAssociations[tableId] || [];\r\n    return allData.filter((item) => tablePasswords.includes(item.id));\r\n  };\r\n\r\n  // Associate a password with a table\r\n  const associatePasswordWithTable = (tableId, passwordId) => {\r\n    setTablePasswordAssociations((prev) => ({\r\n      ...prev,\r\n      [tableId]: [...(prev[tableId] || []), passwordId],\r\n    }));\r\n  };\r\n\r\n  // Remove password association from table\r\n  const removePasswordFromTable = (tableId, passwordId) => {\r\n    setTablePasswordAssociations((prev) => ({\r\n      ...prev,\r\n      [tableId]: (prev[tableId] || []).filter((id) => id !== passwordId),\r\n    }));\r\n  };\r\n\r\n  // Get team members for avatar display - FIXED: Proper backend integration\r\n  const getTeamMembers = () => {\r\n    const members = [];\r\n    // Add current user first\r\n    if (userData) {\r\n      members.push({\r\n        id: userData.id,\r\n        fname: userData.fname || \"User\",\r\n        lname: userData.lname || \"\",\r\n        photo: userData.photo || null,\r\n      });\r\n    }\r\n    // FIXED: Add team members from backend data with proper structure\r\n    if (userData?.teams && userData.teams.length > 0) {\r\n      userData.teams.forEach((team) => {\r\n        // Check if team has users (members) loaded\r\n        if (team.users && Array.isArray(team.users)) {\r\n          team.users.forEach((member) => {\r\n            // Avoid duplicate current user\r\n            if (member.id !== userData.id) {\r\n              members.push({\r\n                id: member.id,\r\n                fname: member.fname || \"Team\",\r\n                lname: member.lname || \"Member\",\r\n                photo: member.photo || null,\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n    // If no team members found, add some placeholder members for demo\r\n    if (members.length === 1) {\r\n      for (let i = 2; i <= 4; i++) {\r\n        members.push({\r\n          id: `placeholder_${i}`,\r\n          fname: `Team`,\r\n          lname: `Member ${i}`,\r\n          photo: null,\r\n        });\r\n      }\r\n    }\r\n    return members.slice(0, 4); // Show max 4 avatars\r\n  };\r\n\r\n  // Toggle password visibility for specific table\r\n  const togglePasswordVisibility = (tableId, cardId) => {\r\n    const currentState = getTableState(tableId);\r\n    updateTableState(tableId, {\r\n      visiblePasswords: {\r\n        ...currentState.visiblePasswords,\r\n        [cardId]: !currentState.visiblePasswords[cardId],\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle edit for specific table\r\n  const handleEdit = (tableId, cardId) => {\r\n    const currentState = getTableState(tableId);\r\n    if (currentState.editingRowId === cardId) {\r\n      // Exit edit mode\r\n      updateTableState(tableId, { editingRowId: null });\r\n    } else {\r\n      // Enter edit mode\r\n      updateTableState(tableId, { editingRowId: cardId });\r\n    }\r\n  };\r\n\r\n  // UPDATED: Handle inline edit save with proper dropdown support\r\n  const handleInlineEditSave = async (tableId, cardId, field, value) => {\r\n    try {\r\n      const item = dataItems?.data?.find((item) => item.id === cardId);\r\n      if (!item) {\r\n        toast.error(\"Item not found\");\r\n        return;\r\n      }\r\n      // UPDATED: Proper data structure for backend with dropdown support\r\n      let updateData = {\r\n        password_title: item.password_title || item.title,\r\n        username: item.username,\r\n        password: item.password,\r\n        department_id: item.department_id,\r\n        team_id: item.team_id,\r\n        user_id: item.user_id || currentUserId, // Always include user_id for backend validation\r\n      };\r\n      // UPDATED: Handle different field updates including dropdown selections\r\n      if (field === \"title\") {\r\n        updateData.password_title = value;\r\n      } else if (field === \"username\") {\r\n        updateData.username = value;\r\n      } else if (field === \"password\") { // Handle password update\r\n         updateData.password = value;\r\n      } else if (field === \"team\") {\r\n        // Handle team dropdown selection - value should be team ID\r\n        updateData.team_id = value ? parseInt(value) : null;\r\n      } else if (field === \"department\") {\r\n        // Handle department dropdown selection - value should be department ID\r\n        updateData.department_id = value ? parseInt(value) : null;\r\n        // Reset team when department changes\r\n        updateData.team_id = null;\r\n      }\r\n      console.log(\"Updating with data:\", updateData); // Debug log\r\n      await updatePasswordManager({ id: cardId, ...updateData }).unwrap();\r\n      // FIXED: Await refetch to ensure data is refreshed before proceeding\r\n      await refetch();\r\n      // FIXED: Auto-exit editing mode after successful save\r\n      updateTableState(tableId, { editingRowId: null });\r\n      toast.success(\"Successfully updated!\");\r\n    } catch (error) {\r\n      console.error(\"Error updating:\", error);\r\n      // Improved error handling for duplicate title\r\n      if (error?.data?.errors?.password_title?.[0]) {\r\n        toast.error(error.data.errors.password_title[0]);\r\n      } else {\r\n        toast.error(\r\n          error?.data?.message || \"Failed to update. Please try again.\"\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle delete\r\n  const handleDelete = async (cardId) => {\r\n    confirmationAlert({\r\n      onConfirm: async () => {\r\n        try {\r\n          await deletePasswordManager(cardId).unwrap();\r\n          // Remove from all table associations\r\n          Object.keys(tablePasswordAssociations).forEach((tableId) => {\r\n            removePasswordFromTable(parseInt(tableId), cardId);\r\n          });\r\n          refetch();\r\n          toast.success(\"Password deleted successfully!\");\r\n        } catch (error) {\r\n          console.error(\"Error deleting password:\", error);\r\n          toast.error(\"Failed to delete password. Please try again.\");\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle successful password creation/update - FIXED: Reset form properly\r\n  const handlePasswordSuccess = (tableId, newPasswordId = null) => {\r\n    refetch().then(() => {\r\n      // If a new password was created, associate it with the table\r\n      if (newPasswordId) {\r\n        associatePasswordWithTable(tableId, newPasswordId);\r\n      }\r\n    });\r\n    updateTableState(tableId, {\r\n      showAddForm: false,\r\n      editingRowId: null,\r\n    });\r\n    // FIXED: Only one success notification, no duplicate\r\n    toast.success(\"Password saved successfully!\");\r\n  };\r\n\r\n  // Toggle shareable card selection\r\n  const toggleShareableCard = (tableId, cardId) => {\r\n    const currentState = getTableState(tableId);\r\n    const currentShareable = currentState.shareableCards;\r\n    const newShareable = currentShareable.includes(cardId)\r\n      ? currentShareable.filter((id) => id !== cardId)\r\n      : [...currentShareable, cardId];\r\n    updateTableState(tableId, { shareableCards: newShareable });\r\n  };\r\n\r\n  // Toggle all shareable cards\r\n  const toggleAllShareable = (tableId) => {\r\n    const currentState = getTableState(tableId);\r\n    const tableData = dataItems?.data || [];\r\n    if (currentState.shareableCards.length === tableData.length) {\r\n      updateTableState(tableId, { shareableCards: [] });\r\n    } else {\r\n      updateTableState(tableId, {\r\n        shareableCards: tableData.map((card) => card.id),\r\n      });\r\n    }\r\n  };\r\n\r\n  // FIXED: Handle share - Now copies selected rows to clipboard\r\n  const handleShare = async (tableId) => {\r\n    const currentState = getTableState(tableId);\r\n    const tableData = getTableData(tableId);\r\n    const selectedCards = tableData.filter((card) =>\r\n      currentState.shareableCards.includes(card.id)\r\n    );\r\n\r\n    if (selectedCards.length === 0) {\r\n      toast.error(\"Please select at least one row to copy.\");\r\n      return;\r\n    }\r\n\r\n    // Format the selected rows as a string (e.g., \"Title: title, Username: username, Password: password\")\r\n    const copiedText = selectedCards\r\n      .map(\r\n        (card) =>\r\n          `Title: ${card.password_title || card.title}, ` +\r\n          `Username: ${card.username}, ` +\r\n          `Password: ${card.password}`\r\n      )\r\n      .join(\"\\n\");\r\n\r\n    // Copy to clipboard\r\n    try {\r\n      await navigator.clipboard.writeText(copiedText);\r\n      toast.success(`Successfully copied ${selectedCards.length} row(s) to clipboard!`);\r\n      // Clear selections after copy\r\n      updateTableState(tableId, { shareableCards: [] });\r\n    } catch (error) {\r\n      console.error(\"Error copying to clipboard:\", error);\r\n      toast.error(\"Failed to copy rows. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // --- UPDATED FUNCTION: Handle delete entire table entries or selected entries ---\r\n  const handleDeleteTableEntries = async (tableId) => {\r\n    const currentState = getTableState(tableId);\r\n    const tableData = getTableData(tableId); // Gets passwords associated with this table\r\n\r\n    // FIXED: Special handling for empty tables - Delete the entire table directly\r\n    if (tableData.length === 0) {\r\n      if (tables.length <= 1) {\r\n        toast.error(\"Cannot delete the last table. At least one table must remain.\");\r\n        return;\r\n      }\r\n\r\n      confirmationAlert({\r\n        title: \"Delete Empty Table?\",\r\n        text: `Are you sure you want to delete the empty table \"${getTableState(tableId).headerTitle}\"? This action cannot be undone.`,\r\n        onConfirm: async () => {\r\n          try {\r\n            // Remove the table from state and associations (no backend deletions needed since empty)\r\n            setTables((prev) => prev.filter((table) => table.id !== tableId));\r\n            setTableStates((prev) => {\r\n              const newState = { ...prev };\r\n              delete newState[tableId];\r\n              return newState;\r\n            });\r\n            setTablePasswordAssociations((prev) => {\r\n              const newState = { ...prev };\r\n              delete newState[tableId];\r\n              return newState;\r\n            });\r\n\r\n            // Refetch (though not strictly needed for empty table)\r\n            await refetch();\r\n\r\n            toast.success(`Empty table \"${getTableState(tableId)?.headerTitle || 'Unknown'}\" deleted successfully.`);\r\n          } catch (error) {\r\n            console.error(\"Error deleting empty table:\", error);\r\n            toast.error(\"Failed to delete table. Please try again.\");\r\n          }\r\n        },\r\n      });\r\n      return;\r\n    }\r\n\r\n    // For non-empty tables: Existing logic\r\n    if (currentState.shareableCards.length === 0) {\r\n      toast.error(\"Please select at least one row to delete.\");\r\n      return;\r\n    }\r\n\r\n    const userOwnedCards = currentState.shareableCards.filter((cardId) => {\r\n      const card = tableData.find((c) => c.id === cardId);\r\n      return card && card.user_id === currentUserId;\r\n    });\r\n\r\n    if (userOwnedCards.length === 0) {\r\n      toast.error(\"You can only delete password entries that you created.\");\r\n      return;\r\n    }\r\n\r\n    // FIXED: Special logic for secondary tables (id > 1)\r\n    const isSecondaryTable = tableId > 1;\r\n    const allEntriesSelected = tableData.length > 0 && currentState.shareableCards.length === tableData.length;\r\n\r\n    if (isSecondaryTable || allEntriesSelected) {\r\n      // For secondary tables (any selection) or primary table (all selected): Delete entire table\r\n      if (tables.length <= 1) {\r\n        // Prevent deletion if it's the last table\r\n        // Clear associations instead\r\n        setTablePasswordAssociations(prev => ({\r\n          ...prev,\r\n          [tableId]: [] // Clear password associations for this table\r\n        }));\r\n        updateTableState(tableId, { shareableCards: [] }); // Clear selection\r\n        await refetch();\r\n        toast.success(`All entries cleared from the last table.`);\r\n        return;\r\n      }\r\n\r\n      confirmationAlert({\r\n        title: \"Delete Entire Table?\",\r\n        text: `Are you sure you want to delete the entire table \"${getTableState(tableId).headerTitle}\" and all its entries? This action cannot be undone.`,\r\n        onConfirm: async () => {\r\n          try {\r\n            // 1. Delete user-owned password entries from backend\r\n            const tableData = getTableData(tableId);\r\n            const userOwnedCardIds = tableData\r\n              .filter(card => card.user_id === currentUserId)\r\n              .map(card => card.id);\r\n\r\n            if (userOwnedCardIds.length > 0) {\r\n              await Promise.all(\r\n                userOwnedCardIds.map((cardId) =>\r\n                  deletePasswordManager(cardId).unwrap()\r\n                )\r\n              );\r\n            }\r\n\r\n            // 2. Remove the table from state and associations\r\n            setTables((prev) => prev.filter((table) => table.id !== tableId));\r\n            setTableStates((prev) => {\r\n              const newState = { ...prev };\r\n              delete newState[tableId];\r\n              return newState;\r\n            });\r\n            setTablePasswordAssociations((prev) => {\r\n              const newState = { ...prev };\r\n              delete newState[tableId];\r\n              return newState;\r\n            });\r\n\r\n            // 3. Refetch data\r\n            await refetch();\r\n\r\n            toast.success(`Table \"${getTableState(tableId)?.headerTitle || 'Unknown'}\" and its entries deleted successfully.`);\r\n          } catch (error) {\r\n            console.error(\"Error deleting table or entries:\", error);\r\n            toast.error(\"Failed to delete table or some entries. Please try again.\");\r\n          }\r\n        },\r\n      });\r\n      return;\r\n    }\r\n\r\n    // For primary table (partial selection): Delete only selected entries\r\n    confirmationAlert({\r\n      onConfirm: async () => {\r\n        try {\r\n          await Promise.all(\r\n            userOwnedCards.map((cardId) =>\r\n              deletePasswordManager(cardId).unwrap()\r\n            )\r\n          );\r\n          // Remove from table associations\r\n          userOwnedCards.forEach((cardId) => {\r\n            removePasswordFromTable(tableId, cardId);\r\n          });\r\n          updateTableState(tableId, { shareableCards: [] });\r\n          await refetch();\r\n          toast.success(\r\n            `Successfully deleted ${userOwnedCards.length} password entries from this table.`\r\n          );\r\n        } catch (error) {\r\n          console.error(\"Error deleting password entries:\", error);\r\n          toast.error(\r\n            \"Failed to delete some password entries. Please try again.\"\r\n          );\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center py-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 px-4 sm:px-6 lg:px-8\">\r\n      {/* Render all tables */}\r\n      {tables.map((table) => {\r\n        const tableState = getTableState(table.id);\r\n        const teamMembers = getTeamMembers();\r\n        const tableData = getTableData(table.id);\r\n        return (\r\n          <div key={table.id} className=\"mb-6 sm:mb-8\">\r\n            {/* RESPONSIVE Table Header */}\r\n            <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6 gap-4 sm:gap-0\">\r\n              <div className=\"flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 w-full sm:w-auto\">\r\n                {/* Editable Table Title */}\r\n                <div className=\"flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto\">\r\n                  {tableState.editingHeader ? (\r\n                    <input\r\n                      type=\"text\"\r\n                      value={tableState.headerTitle}\r\n                      onChange={(e) =>\r\n                        updateTableState(table.id, {\r\n                          headerTitle: e.target.value,\r\n                        })\r\n                      }\r\n                      onBlur={() =>\r\n                        updateTableState(table.id, { editingHeader: false })\r\n                      }\r\n                      onKeyDown={(e) => {\r\n                        if (e.key === \"Enter\") {\r\n                          updateTableState(table.id, { editingHeader: false });\r\n                        }\r\n                      }}\r\n                      className=\"w-full sm:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                      autoFocus\r\n                    />\r\n                  ) : (\r\n                    <h2\r\n                      className=\"text-base sm:text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors\"\r\n                      onClick={() =>\r\n                        updateTableState(table.id, { editingHeader: true })\r\n                      }\r\n                      title=\"Click to edit table name\"\r\n                    >\r\n                      {tableState.headerTitle}\r\n                    </h2>\r\n                  )}\r\n\r\n                  {/* Action Buttons - Mobile First Layout */}\r\n                  <div className=\"flex items-center space-x-1 sm:space-x-2\">\r\n                    {/* Share Icon */}\r\n                    <button\r\n                      onClick={() => handleShare(table.id)}\r\n                      className=\"flex items-center justify-center py-1 px-2 sm:px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs sm:text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                      title=\"Copy selected rows to clipboard\"\r\n                    >\r\n                      <span className=\"material-symbols-outlined text-sm\">\r\n                        share\r\n                      </span>\r\n                    </button>\r\n                    {/* Delete Selected Icon */}\r\n                    <button\r\n                      onClick={() => handleDeleteTableEntries(table.id)}\r\n                      className=\"flex items-center justify-center py-1 px-2 sm:px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-xs sm:text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                      title=\"Delete selected entries or table\"\r\n                    >\r\n                      <span className=\"material-symbols-outlined text-sm\">\r\n                        delete\r\n                      </span>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Team Member Avatars - Responsive */}\r\n                <div className=\"flex items-center -space-x-2 ml-0 sm:ml-2\">\r\n                  {teamMembers.slice(0, 3).map((member, index) => (\r\n                    <div key={index} className=\"relative\">\r\n                      {member?.photo ? (\r\n                        <img\r\n                          src={\r\n                            member?.photo\r\n                              ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${member.photo}`\r\n                              : \"/default-avatar.png\"\r\n                          }\r\n                          alt={`${member.fname || \"User\"} ${\r\n                            member.lname || \"\"\r\n                          }`}\r\n                          className=\"w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover border-2 border-white shadow-sm\"\r\n                          onError={(e) => {\r\n                            e.target.style.display = \"none\";\r\n                            e.target.nextSibling.style.display = \"flex\";\r\n                          }}\r\n                        />\r\n                      ) : null}\r\n                      {/* Fallback avatar */}\r\n                      <div\r\n                        className=\"w-6 h-6 sm:w-8 sm:h-8 bg-primary rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm\"\r\n                        style={{ display: member?.photo ? \"none\" : \"flex\" }}\r\n                      >\r\n                        {member?.fname?.charAt(0) || \"T\"}\r\n                        {member?.lname?.charAt(0) || \"M\"}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {teamMembers.length > 3 && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white shadow-sm\">\r\n                      +{teamMembers.length - 3}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Add Password Button - Responsive */}\r\n              <button\r\n                onClick={() => {\r\n                  updateTableState(table.id, {\r\n                    editingRowId: null,\r\n                    showAddForm: !tableState.showAddForm,\r\n                  });\r\n                }}\r\n                className=\"flex items-center justify-center py-2 px-3 sm:px-4 text-xs sm:text-sm font-medium bg-transparent text-black border-2 border-[#0B333F] rounded-full hover:bg-primary hover:text-white transition duration-500 ease-in-out focus:outline-none focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 shadow-sm w-full sm:w-auto sm:min-w-[200px] h-[36px] sm:h-[40px]\"\r\n              >\r\n                <svg\r\n                  className=\"w-4 h-4 sm:w-6 sm:h-6 mr-1 sm:mr-2 -ml-2 sm:-ml-3 flex items-center justify-center rounded-full bg-white border-2 border-black\"\r\n                  fill=\"none\"\r\n                  stroke=\"black\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"\r\n                  />\r\n                </svg>\r\n                Add Password\r\n              </button>\r\n            </div>\r\n\r\n            {/* Add Password Form */}\r\n            {tableState.showAddForm && (\r\n              <div className=\"mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200\">\r\n                <AddPasswordCardForm\r\n                  onCancel={() => {\r\n                    updateTableState(table.id, {\r\n                      showAddForm: false,\r\n                      editingRowId: null,\r\n                    });\r\n                  }}\r\n                  onSuccess={(newPasswordId) =>\r\n                    handlePasswordSuccess(table.id, newPasswordId)\r\n                  }\r\n                  generatedPassword={generatedPassword}\r\n                  passwordStrength={passwordStrength}\r\n                  editData={\r\n                    tableState.editingRowId\r\n                      ? tableData.find((item) => item.id === tableState.editingRowId)\r\n                      : null\r\n                  }\r\n                  departmentsData={departmentsData}\r\n                  teamsData={teamsData}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* RESPONSIVE Table Container */}\r\n            <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\r\n              <div className=\"overflow-x-auto\">\r\n                {tableData.length === 0 ? (\r\n                  <div className=\"text-center py-8 sm:py-12 px-4\">\r\n                    <p className=\"text-gray-500 mb-4 text-sm sm:text-base\">\r\n                      No password cards added yet. Click \"Add Password\" to\r\n                      add your first card.\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  <table className=\"w-full text-xs sm:text-sm text-left min-w-[800px]\">\r\n                    <thead className=\"bg-gray-50 border-b border-gray-200\">\r\n                      <tr>\r\n                        <th scope=\"col\" className=\"px-2 sm:px-4 py-2 sm:py-3 w-8 sm:w-12\">\r\n                          <input\r\n                            type=\"checkbox\"\r\n                            checked={\r\n                              tableState.shareableCards.length ===\r\n                                tableData.length && tableData.length > 0\r\n                            }\r\n                            onChange={() => toggleAllShareable(table.id)}\r\n                            className=\"w-3 h-3 sm:w-4 sm:h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\r\n                          />\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]\"\r\n                        >\r\n                          <div className=\"flex items-center\">\r\n                            Title\r\n                            <svg\r\n                              className=\"w-3 h-3 ml-1 text-gray-400\"\r\n                              fill=\"currentColor\"\r\n                              viewBox=\"0 0 20 20\"\r\n                            >\r\n                              <path\r\n                                fillRule=\"evenodd\"\r\n                                d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n                                clipRule=\"evenodd\"\r\n                              />\r\n                            </svg>\r\n                          </div>\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]\"\r\n                        >\r\n                          <div className=\"flex items-center\">\r\n                            User Name\r\n                            <svg\r\n                              className=\"w-3 h-3 ml-1 text-gray-400\"\r\n                              fill=\"currentColor\"\r\n                              viewBox=\"0 0 20 20\"\r\n                            >\r\n                              <path\r\n                                fillRule=\"evenodd\"\r\n                                d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n                                clipRule=\"evenodd\"\r\n                              />\r\n                            </svg>\r\n                          </div>\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px] sm:min-w-[150px]\"\r\n                        >\r\n                          Password\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px] sm:min-w-[120px]\"\r\n                        >\r\n                          Team\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px] sm:min-w-[120px]\"\r\n                        >\r\n                          Department\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px] sm:min-w-[100px]\"\r\n                        >\r\n                          Level\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px] sm:min-w-[100px]\"\r\n                        >\r\n                          Action\r\n                        </th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                      {tableData.map((card) => (\r\n                        <TableRow\r\n                          key={card.id}\r\n                          card={card}\r\n                          tableId={table.id}\r\n                          isEditing={tableState.editingRowId === card.id}\r\n                          isShareable={tableState.shareableCards.includes(\r\n                            card.id\r\n                          )}\r\n                          visiblePassword={tableState.visiblePasswords[card.id]}\r\n                          currentUserId={currentUserId}\r\n                          // PASS CORRECT DROPDOWN DATA TO TableRow\r\n                          departmentsData={departmentsData}\r\n                          teamsData={teamsData}\r\n                          onEdit={handleEdit}\r\n                          onDelete={handleDelete}\r\n                          onTogglePassword={togglePasswordVisibility}\r\n                          onToggleShareable={toggleShareableCard}\r\n                          onInlineEditSave={handleInlineEditSave}\r\n                        />\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n                )}\r\n              </div>\r\n            </div>\r\n            \r\n            {/* RESPONSIVE Add New Password Card Button */}\r\n            <div className=\"flex justify-center mt-4 sm:mt-6\">\r\n              <button\r\n                onClick={handleAddNewTable}\r\n                className=\"flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 bg-[#006D94] text-white rounded-lg shadow-md hover:bg-[#005F80] transition duration-200 text-sm sm:text-base w-full sm:w-auto max-w-xs sm:max-w-none\"\r\n              >\r\n                {/* Circular Plus Icon */}\r\n                <div className=\"w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center border-2 border-white rounded-full flex-shrink-0\">\r\n                  <span className=\"relative -top-[1px] sm:-top-[2px] text-base sm:text-lg font-thin\">\r\n                    +\r\n                  </span>\r\n                </div>\r\n                {/* Button Text */}\r\n                <span className=\"text-xs sm:text-sm font-medium\">\r\n                  Add New Password Card\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\n// UPDATED RESPONSIVE TableRow component\r\nconst TableRow = ({\r\n  card,\r\n  tableId,\r\n  isEditing,\r\n  isShareable,\r\n  visiblePassword,\r\n  currentUserId,\r\n  departmentsData = [],\r\n  teamsData = [],\r\n  onEdit,\r\n  onDelete,\r\n  onTogglePassword,\r\n  onToggleShareable,\r\n  onInlineEditSave,\r\n}) => {\r\n  // --- UPDATED: Proper initialization of edit values including password ---\r\n  const [editValues, setEditValues] = useState({\r\n    title: card.password_title || card.title || card.user_name || \"\",\r\n    username: card.username || card.user_name || \"\",\r\n    password: card.password || \"\",\r\n    team_id: card.team_id ? String(card.team_id) : \"\",\r\n    department_id: card.department_id ? String(card.department_id) : \"\",\r\n  });\r\n\r\n  // FIXED: Sync editValues with updated card props after refetch\r\n  useEffect(() => {\r\n    setEditValues({\r\n      title: card.password_title || card.title || card.user_name || \"\",\r\n      username: card.username || card.user_name || \"\",\r\n      password: card.password || \"\",\r\n      team_id: card.team_id ? String(card.team_id) : \"\",\r\n      department_id: card.department_id ? String(card.department_id) : \"\",\r\n    });\r\n  }, [card]);\r\n\r\n  // --- UPDATED: Handle input changes with department/team relationship logic and password ---\r\n  const handleInputChange = (field, value) => {\r\n    if (field === \"department_id\") {\r\n      setEditValues((prev) => ({\r\n        ...prev,\r\n        [field]: value,\r\n        team_id: \"\",\r\n      }));\r\n    } else {\r\n      setEditValues((prev) => ({ ...prev, [field]: value }));\r\n    }\r\n  };\r\n\r\n  // --- UPDATED: Proper field value comparison and update for all fields including password ---\r\n  const handleInputBlur = (field) => {\r\n    let originalValue;\r\n    if (field === \"title\") {\r\n      originalValue = card.password_title || card.title || card.user_name;\r\n    } else if (field === \"username\") {\r\n      originalValue = card.username || card.user_name;\r\n    } else if (field === \"password\") {\r\n       originalValue = card.password;\r\n    } else if (field === \"team_id\") {\r\n      originalValue = card.team_id ? String(card.team_id) : \"\";\r\n    } else if (field === \"department_id\") {\r\n      originalValue = card.department_id ? String(card.department_id) : \"\";\r\n    }\r\n    // Client-side validation for required fields\r\n    const requiredFields = [\"title\", \"username\", \"password\", \"department_id\", \"team_id\"];\r\n    if (requiredFields.includes(field) && !editValues[field]) {\r\n      toast.error(`The ${field.replace('_', ' ')} field is required.`);\r\n      return;\r\n    }\r\n    if (editValues[field] !== originalValue) {\r\n      const backendField =\r\n        field === \"team_id\"\r\n          ? \"team\"\r\n          : field === \"department_id\"\r\n          ? \"department\"\r\n          : field;\r\n      onInlineEditSave(tableId, card.id, backendField, editValues[field]);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === \"Enter\") {\r\n      e.target.blur();\r\n    }\r\n  };\r\n\r\n  // Get password strength styling\r\n  const getPasswordStrengthStyle = (level) => {\r\n    switch (level) {\r\n      case \"Strong\":\r\n        return \"bg-green/10 text-[#22C55E]\";\r\n      case \"Moderate\":\r\n        return \"bg-yellow/10 text-[#F59E0B]\";\r\n      case \"Weak\":\r\n        return \"bg-red/10 text-[#EF4444]\";\r\n      default:\r\n        return \"bg-green-100 text-green-700 border border-green-200\";\r\n    }\r\n  };\r\n\r\n  // UPDATED: Get filtered teams based on selected department\r\n  const getFilteredTeams = () => {\r\n    if (!editValues.department_id || !Array.isArray(teamsData)) return [];\r\n    return teamsData.filter(\r\n      (team) =>\r\n        team.department_ids &&\r\n        team.department_ids.includes(parseInt(editValues.department_id))\r\n    );\r\n  };\r\n\r\n  return (\r\n    <tr className=\"hover:bg-gray-50 transition-colors\">\r\n      {/* Checkbox Column */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        <input\r\n          type=\"checkbox\"\r\n          checked={isShareable}\r\n          onChange={() => onToggleShareable(tableId, card.id)}\r\n          className=\"w-3 h-3 sm:w-4 sm:h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\r\n        />\r\n      </td>\r\n      \r\n      {/* Title Column */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        <div className=\"flex items-center\">\r\n          {/* Platform Icon Circle - Responsive */}\r\n          <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-2 sm:mr-3 text-xs sm:text-sm flex-shrink-0\">\r\n            {(card.password_title || card.title || card.user_name)\r\n              ?.charAt(0)\r\n              ?.toUpperCase() || \"P\"}\r\n          </div>\r\n          {isEditing ? (\r\n            <input\r\n              type=\"text\"\r\n              value={editValues.title}\r\n              onChange={(e) => handleInputChange(\"title\", e.target.value)}\r\n              onBlur={() => handleInputBlur(\"title\")}\r\n              onKeyDown={handleKeyDown}\r\n              className=\"font-medium text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-primary focus:border-blue-500 text-xs sm:text-sm\"\r\n              autoFocus\r\n              placeholder=\"Platform Title\"\r\n            />\r\n          ) : (\r\n            <span className=\"font-medium text-gray-900 text-xs sm:text-sm break-words\">\r\n              {card.password_title || card.title || card.user_name}\r\n            </span>\r\n          )}\r\n        </div>\r\n      </td>\r\n      \r\n      {/* User Name Column */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        <div className=\"flex items-center\">\r\n          {isEditing ? (\r\n            <input\r\n              type=\"text\"\r\n              value={editValues.username}\r\n              onChange={(e) => handleInputChange(\"username\", e.target.value)}\r\n              onBlur={() => handleInputBlur(\"username\")}\r\n              onKeyDown={handleKeyDown}\r\n              className=\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-1 sm:mr-2 focus:ring-2 focus:ring-primary focus:border-blue-500 text-xs sm:text-sm\"\r\n              placeholder=\"Username or Email\"\r\n            />\r\n          ) : (\r\n            <span className=\"text-gray-900 mr-1 sm:mr-2 text-xs sm:text-sm break-all\">\r\n              {card.username || card.user_name}\r\n            </span>\r\n          )}\r\n          {/* Copy Username Icon - Responsive */}\r\n          <button\r\n            className=\"flex items-center justify-center py-1 px-1 sm:px-2 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 flex-shrink-0\"\r\n            title=\"Copy username\"\r\n            onClick={() => {\r\n              const username = card.username || card.user_name;\r\n              if (username) {\r\n                navigator.clipboard.writeText(username);\r\n                toast.success(\"Username copied to clipboard!\");\r\n              }\r\n            }}\r\n          >\r\n            <span className=\"material-symbols-outlined text-sm\">\r\n              content_copy\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </td>\r\n      \r\n      {/* Password Column - Responsive */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        <div className=\"flex items-center\">\r\n          {isEditing ? (\r\n            <>\r\n              <input\r\n                type={visiblePassword ? \"text\" : \"password\"}\r\n                value={editValues.password}\r\n                onChange={(e) => handleInputChange(\"password\", e.target.value)}\r\n                onBlur={() => handleInputBlur(\"password\")}\r\n                onKeyDown={handleKeyDown}\r\n                className=\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-1 focus:ring-2 focus:ring-primary focus:border-blue-500 font-mono text-xs sm:text-sm\"\r\n                placeholder=\"Password\"\r\n              />\r\n              <button\r\n                onClick={() => onTogglePassword(tableId, card.id)}\r\n                className=\"flex items-center justify-center py-1 px-1 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 mr-1 flex-shrink-0\"\r\n                title={visiblePassword ? \"Hide password\" : \"Show password\"}\r\n                type=\"button\"\r\n              >\r\n                <span className=\"material-symbols-outlined text-sm\">\r\n                  {visiblePassword ? \"visibility_off\" : \"visibility\"}\r\n                </span>\r\n              </button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <span className=\"text-gray-900 mr-1 font-mono text-xs sm:text-sm break-all\">\r\n                {visiblePassword ? card.password : \"••••••••••••\"}\r\n              </span>\r\n              <button\r\n                onClick={() => onTogglePassword(tableId, card.id)}\r\n                className=\"flex items-center justify-center py-1 px-1 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 mr-1 flex-shrink-0\"\r\n                title={visiblePassword ? \"Hide password\" : \"Show password\"}\r\n                type=\"button\"\r\n              >\r\n                <span className=\"material-symbols-outlined text-sm\">\r\n                  {visiblePassword ? \"visibility_off\" : \"visibility\"}\r\n                </span>\r\n              </button>\r\n            </>\r\n          )}\r\n          {/* Copy Password Icon - Responsive */}\r\n          <button\r\n            className=\"flex items-center justify-center py-1 px-1 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200 flex-shrink-0\"\r\n            title=\"Copy password\"\r\n            onClick={() => {\r\n              const passwordToCopy = isEditing\r\n                ? editValues.password\r\n                : card.password;\r\n              if (passwordToCopy) {\r\n                navigator.clipboard.writeText(passwordToCopy);\r\n                toast.success(\"Password copied to clipboard!\");\r\n              }\r\n            }}\r\n            type=\"button\"\r\n          >\r\n            <span className=\"material-symbols-outlined text-sm\">\r\n              content_copy\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </td>\r\n      \r\n      {/* Team Column - Responsive Dropdown */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        {isEditing ? (\r\n          <select\r\n            value={editValues.team_id}\r\n            onChange={(e) => handleInputChange(\"team_id\", e.target.value)}\r\n            onBlur={() => handleInputBlur(\"team_id\")}\r\n            disabled={!editValues.department_id}\r\n            className={`text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm ${\r\n              !editValues.department_id ? \"bg-gray-100 cursor-not-allowed\" : \"\"\r\n            }`}\r\n          >\r\n            <option value=\"\">Select Team</option>\r\n            {getFilteredTeams().map((team) => (\r\n              <option key={team.id} value={team.id}>\r\n                {team.name}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        ) : (\r\n          <span className=\"text-gray-900 text-xs sm:text-sm break-words\">\r\n            {card.team?.name || \"Team Name\"}\r\n          </span>\r\n        )}\r\n      </td>\r\n      \r\n      {/* Department Column - Responsive Dropdown */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        {isEditing ? (\r\n          <select\r\n            value={editValues.department_id || \"\"}\r\n            onChange={(e) => handleInputChange(\"department_id\", e.target.value)}\r\n            onBlur={() => handleInputBlur(\"department_id\")}\r\n            className=\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm\"\r\n          >\r\n            <option value=\"\">Select Department</option>\r\n            {departmentsData.map((dept) => (\r\n              <option key={dept.id} value={dept.id}>\r\n                {dept.name}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        ) : (\r\n          <span className=\"text-gray-900 text-xs sm:text-sm break-words\">\r\n            {card.department?.name || \"Department name\"}\r\n          </span>\r\n        )}\r\n      </td>\r\n      \r\n      {/* Level Column - Responsive */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        <span\r\n          className={`px-2 py-1 text-xs font-medium rounded ${getPasswordStrengthStyle(\r\n            card.level\r\n          )}`}\r\n        >\r\n          {card.level || \"Strong Password\"}\r\n        </span>\r\n      </td>\r\n      \r\n      {/* Action Column - Responsive */}\r\n      <td className=\"px-2 sm:px-4 py-3 sm:py-4\">\r\n        <div className=\"flex items-center space-x-1\">\r\n          {/* Edit Button */}\r\n          <button\r\n            onClick={() => onEdit(tableId, card.id)}\r\n            className=\"flex items-center justify-center py-1 px-1 sm:px-2 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200\"\r\n            title=\"Edit\"\r\n          >\r\n            <span className=\"material-symbols-outlined text-sm\">\r\n              stylus_note\r\n            </span>\r\n          </button>\r\n          {/* Delete Icon - Only for card owner */}\r\n          {card.user_id === currentUserId && (\r\n            <button\r\n              onClick={() => onDelete(card.id)}\r\n              className=\"flex items-center justify-center py-1 px-1 sm:px-2 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-xs focus:outline-none focus:ring-2 focus:ring-gray-200\"\r\n              title=\"Delete\"\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">\r\n                delete\r\n              </span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      </td>\r\n    </tr>\r\n  );\r\n};\r\n\r\nexport default PasswordCardsTable;"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CACvD,OAASC,iBAAiB,KAAQ,qBAAqB,CACvD,MAAO,CAAAC,iBAAiB,KAAM,0CAA0C,CACxE,OACEC,8BAA8B,CAC9BC,gCAAgC,CAChCC,gCAAgC,KAC3B,uCAAuC,CAC9C;AACA,OAASC,+BAA+B,KAAQ,kCAAkC,CAClF,OAASC,+BAA+B,KAAQ,4BAA4B,CAC5E,OAASC,KAAK,KAAQ,QAAQ,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/B,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAA6C,IAA5C,CAAEC,iBAAiB,CAAEC,gBAAiB,CAAC,CAAAF,IAAA,CACjE;AACA,KAAM,CAAEG,QAAS,CAAC,CAAGjB,iBAAiB,CAAC,CAAC,CACxC,KAAM,CAAAkB,aAAa,CAAGD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEE,EAAE,CAClC;AACA,KAAM,CACJC,IAAI,CAAEC,SAAS,CACfC,SAAS,CACTC,OACF,CAAC,CAAGtB,8BAA8B,CAAC,CACjCuB,OAAO,CAAE,YAAY,CACrBC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,GAAG,CACbC,KAAK,CAAE,EACT,CAAC,CAAC,CACF;AACA,KAAM,CAAER,IAAI,CAAES,eAAgB,CAAC,CAAGzB,+BAA+B,CAAC,CAAC,CACnE,KAAM,CAAEgB,IAAI,CAAEU,SAAS,CAAER,SAAS,CAAES,YAAa,CAAC,CAAG1B,+BAA+B,CAAC,CAAC,CACtF2B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEJ,eAAe,CAAC,CAChDG,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEH,SAAS,CAAC,CACpC,KAAM,CAACI,qBAAqB,CAAC,CAAGhC,gCAAgC,CAAC,CAAC,CAClE,KAAM,CAACiC,qBAAqB,CAAC,CAAGhC,gCAAgC,CAAC,CAAC,CAElE;AACA,KAAM,CAACiC,MAAM,CAAEC,SAAS,CAAC,CAAGzC,QAAQ,CAAC,CAAC,CAAEuB,EAAE,CAAE,CAAC,CAAEmB,IAAI,CAAE,qBAAsB,CAAC,CAAC,CAAC,CAE9E;AACA,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,CAC7C,CAAC,CAAE,CACD6C,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,IAAI,CAClBC,gBAAgB,CAAE,CAAC,CAAC,CACpBC,cAAc,CAAE,EAAE,CAClBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAE,qBACf,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGpD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAE9E;AACAC,SAAS,CAAC,IAAM,CACd,GAAI,CAACqB,aAAa,CAAE,OACpB,KAAM,CAAA+B,UAAU,CAAG,kBAAkB/B,aAAa,EAAE,CACpD,KAAM,CAAAgC,aAAa,CAAGC,YAAY,CAACC,OAAO,CAACH,UAAU,CAAC,CACtD,GAAIC,aAAa,CAAE,CACjB,KAAM,CAAEd,MAAM,CAAEiB,eAAe,CAAEC,YAAY,CAAEC,qBAAsB,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACP,aAAa,CAAC,CAClGb,SAAS,CAACgB,eAAe,CAAC,CAC1BL,4BAA4B,CAACO,qBAAqB,CAAC,CACnD;AACA,KAAM,CAAAG,cAAc,CAAG,CAAC,CAAC,CACzBL,eAAe,CAACM,OAAO,CAACC,KAAK,EAAI,CAC/BF,cAAc,CAACE,KAAK,CAACzC,EAAE,CAAC,CAAGoB,WAAW,CAACqB,KAAK,CAACzC,EAAE,CAAC,EAAI,CAClDsB,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,IAAI,CAClBC,gBAAgB,CAAE,CAAC,CAAC,CACpBC,cAAc,CAAE,EAAE,CAClBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAEc,KAAK,CAACtB,IACrB,CAAC,CACH,CAAC,CAAC,CACFE,cAAc,CAACkB,cAAc,CAAC,CAChC,CACF,CAAC,CAAE,CAACxC,aAAa,CAAC,CAAC,CAEnB;AACArB,SAAS,CAAC,IAAM,CACd,GAAI,CAACqB,aAAa,CAAE,OACpB,KAAM,CAAA+B,UAAU,CAAG,kBAAkB/B,aAAa,EAAE,CACpDiC,YAAY,CAACU,OAAO,CAACZ,UAAU,CAAEO,IAAI,CAACM,SAAS,CAAC,CAC9C1B,MAAM,CACNkB,YAAY,CAAEP,yBAChB,CAAC,CAAC,CAAC,CACL,CAAC,CAAE,CAACX,MAAM,CAAEW,yBAAyB,CAAE7B,aAAa,CAAC,CAAC,CAEtD;AACA,KAAM,CAAC6C,4BAA4B,CAAEC,+BAA+B,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CACvFC,SAAS,CAAC,IAAM,CACd,GACE,CAACkE,4BAA4B,EAC7B1C,SAAS,SAATA,SAAS,WAATA,SAAS,CAAED,IAAI,EACfC,SAAS,CAACD,IAAI,CAAC6C,MAAM,CAAG,CAAC,EACzBC,MAAM,CAACC,IAAI,CAACpB,yBAAyB,CAAC,CAACkB,MAAM,GAAK,CAAE;AAAA,CACpD,CACA,KAAM,CAAAG,mBAAmB,CAAG/C,SAAS,CAACD,IAAI,CAACiD,GAAG,CAAEC,IAAI,EAAKA,IAAI,CAACnD,EAAE,CAAC,CACjE6B,4BAA4B,CAAC,CAAE,CAAC,CAAEoB,mBAAoB,CAAC,CAAC,CACxDJ,+BAA+B,CAAC,IAAI,CAAC,CACvC,CACF,CAAC,CAAE,CAAC3C,SAAS,CAAE0C,4BAA4B,CAAEhB,yBAAyB,CAAC,CAAC,CAExE;AACA,KAAM,CAAAwB,iBAAiB,CAAGA,CAAA,GAAM,CAC9B;AACA,KAAM,CAAAC,UAAU,CAAGpC,MAAM,CAAC6B,MAAM,CAAG,CAAC,CAAGQ,IAAI,CAACC,GAAG,CAAC,GAAGtC,MAAM,CAACiC,GAAG,CAACM,CAAC,EAAIA,CAAC,CAACxD,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACjF,KAAM,CAAAyD,YAAY,CAAG,uBAAuBJ,UAAU,EAAE,CAAE;AAE1D;AACAnC,SAAS,CAAEwC,IAAI,EAAK,CAAC,GAAGA,IAAI,CAAE,CAAE1D,EAAE,CAAEqD,UAAU,CAAElC,IAAI,CAAEsC,YAAa,CAAC,CAAC,CAAC,CAEtE;AACApC,cAAc,CAAEqC,IAAI,GAAM,CACxB,GAAGA,IAAI,CACP,CAACL,UAAU,EAAG,CACZ/B,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,IAAI,CAClBC,gBAAgB,CAAE,CAAC,CAAC,CACpBC,cAAc,CAAE,EAAE,CAClBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAE8B,YACf,CACF,CAAC,CAAC,CAAC,CAEH;AACA5B,4BAA4B,CAAE6B,IAAI,GAAM,CACtC,GAAGA,IAAI,CACP,CAACL,UAAU,EAAG,EAChB,CAAC,CAAC,CAAC,CAEHlE,KAAK,CAACwE,OAAO,CAAC,cAAcF,YAAY,YAAY,CAAC,CACvD,CAAC,CAED;AACA,KAAM,CAAAG,aAAa,CAAIC,OAAO,EAAK,CACjC,MACE,CAAAzC,WAAW,CAACyC,OAAO,CAAC,EAAI,CACtBvC,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,IAAI,CAClBC,gBAAgB,CAAE,CAAC,CAAC,CACpBC,cAAc,CAAE,EAAE,CAClBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAE,uBAAuBkC,OAAO,EAC7C,CAAC,CAEL,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAACD,OAAO,CAAEE,OAAO,GAAK,CAC7C1C,cAAc,CAAEqC,IAAI,GAAM,CACxB,GAAGA,IAAI,CACP,CAACG,OAAO,EAAG,CACT,GAAGD,aAAa,CAACC,OAAO,CAAC,CACzB,GAAGE,OACL,CACF,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAIH,OAAO,EAAK,CAChC,KAAM,CAAAI,OAAO,CAAG,CAAA/D,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAED,IAAI,GAAI,EAAE,CACrC,KAAM,CAAAiE,cAAc,CAAGtC,yBAAyB,CAACiC,OAAO,CAAC,EAAI,EAAE,CAC/D,MAAO,CAAAI,OAAO,CAACE,MAAM,CAAEhB,IAAI,EAAKe,cAAc,CAACE,QAAQ,CAACjB,IAAI,CAACnD,EAAE,CAAC,CAAC,CACnE,CAAC,CAED;AACA,KAAM,CAAAqE,0BAA0B,CAAGA,CAACR,OAAO,CAAES,UAAU,GAAK,CAC1DzC,4BAA4B,CAAE6B,IAAI,GAAM,CACtC,GAAGA,IAAI,CACP,CAACG,OAAO,EAAG,CAAC,IAAIH,IAAI,CAACG,OAAO,CAAC,EAAI,EAAE,CAAC,CAAES,UAAU,CAClD,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAGA,CAACV,OAAO,CAAES,UAAU,GAAK,CACvDzC,4BAA4B,CAAE6B,IAAI,GAAM,CACtC,GAAGA,IAAI,CACP,CAACG,OAAO,EAAG,CAACH,IAAI,CAACG,OAAO,CAAC,EAAI,EAAE,EAAEM,MAAM,CAAEnE,EAAE,EAAKA,EAAE,GAAKsE,UAAU,CACnE,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAG,EAAE,CAClB;AACA,GAAI3E,QAAQ,CAAE,CACZ2E,OAAO,CAACC,IAAI,CAAC,CACX1E,EAAE,CAAEF,QAAQ,CAACE,EAAE,CACf2E,KAAK,CAAE7E,QAAQ,CAAC6E,KAAK,EAAI,MAAM,CAC/BC,KAAK,CAAE9E,QAAQ,CAAC8E,KAAK,EAAI,EAAE,CAC3BC,KAAK,CAAE/E,QAAQ,CAAC+E,KAAK,EAAI,IAC3B,CAAC,CAAC,CACJ,CACA;AACA,GAAI/E,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEgF,KAAK,EAAIhF,QAAQ,CAACgF,KAAK,CAAChC,MAAM,CAAG,CAAC,CAAE,CAChDhD,QAAQ,CAACgF,KAAK,CAACtC,OAAO,CAAEuC,IAAI,EAAK,CAC/B;AACA,GAAIA,IAAI,CAACC,KAAK,EAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAACC,KAAK,CAAC,CAAE,CAC3CD,IAAI,CAACC,KAAK,CAACxC,OAAO,CAAE2C,MAAM,EAAK,CAC7B;AACA,GAAIA,MAAM,CAACnF,EAAE,GAAKF,QAAQ,CAACE,EAAE,CAAE,CAC7ByE,OAAO,CAACC,IAAI,CAAC,CACX1E,EAAE,CAAEmF,MAAM,CAACnF,EAAE,CACb2E,KAAK,CAAEQ,MAAM,CAACR,KAAK,EAAI,MAAM,CAC7BC,KAAK,CAAEO,MAAM,CAACP,KAAK,EAAI,QAAQ,CAC/BC,KAAK,CAAEM,MAAM,CAACN,KAAK,EAAI,IACzB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CACJ,CACA;AACA,GAAIJ,OAAO,CAAC3B,MAAM,GAAK,CAAC,CAAE,CACxB,IAAK,GAAI,CAAAsC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC3BX,OAAO,CAACC,IAAI,CAAC,CACX1E,EAAE,CAAE,eAAeoF,CAAC,EAAE,CACtBT,KAAK,CAAE,MAAM,CACbC,KAAK,CAAE,UAAUQ,CAAC,EAAE,CACpBP,KAAK,CAAE,IACT,CAAC,CAAC,CACJ,CACF,CACA,MAAO,CAAAJ,OAAO,CAACY,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE;AAC9B,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAGA,CAACzB,OAAO,CAAE0B,MAAM,GAAK,CACpD,KAAM,CAAAC,YAAY,CAAG5B,aAAa,CAACC,OAAO,CAAC,CAC3CC,gBAAgB,CAACD,OAAO,CAAE,CACxBrC,gBAAgB,CAAE,CAChB,GAAGgE,YAAY,CAAChE,gBAAgB,CAChC,CAAC+D,MAAM,EAAG,CAACC,YAAY,CAAChE,gBAAgB,CAAC+D,MAAM,CACjD,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAE,UAAU,CAAGA,CAAC5B,OAAO,CAAE0B,MAAM,GAAK,CACtC,KAAM,CAAAC,YAAY,CAAG5B,aAAa,CAACC,OAAO,CAAC,CAC3C,GAAI2B,YAAY,CAACjE,YAAY,GAAKgE,MAAM,CAAE,CACxC;AACAzB,gBAAgB,CAACD,OAAO,CAAE,CAAEtC,YAAY,CAAE,IAAK,CAAC,CAAC,CACnD,CAAC,IAAM,CACL;AACAuC,gBAAgB,CAACD,OAAO,CAAE,CAAEtC,YAAY,CAAEgE,MAAO,CAAC,CAAC,CACrD,CACF,CAAC,CAED;AACA,KAAM,CAAAG,oBAAoB,CAAG,KAAAA,CAAO7B,OAAO,CAAE0B,MAAM,CAAEI,KAAK,CAAEC,KAAK,GAAK,CACpE,GAAI,KAAAC,eAAA,CACF,KAAM,CAAA1C,IAAI,CAAGjD,SAAS,SAATA,SAAS,kBAAA2F,eAAA,CAAT3F,SAAS,CAAED,IAAI,UAAA4F,eAAA,iBAAfA,eAAA,CAAiBC,IAAI,CAAE3C,IAAI,EAAKA,IAAI,CAACnD,EAAE,GAAKuF,MAAM,CAAC,CAChE,GAAI,CAACpC,IAAI,CAAE,CACThE,KAAK,CAAC4G,KAAK,CAAC,gBAAgB,CAAC,CAC7B,OACF,CACA;AACA,GAAI,CAAAC,UAAU,CAAG,CACfC,cAAc,CAAE9C,IAAI,CAAC8C,cAAc,EAAI9C,IAAI,CAAC+C,KAAK,CACjDC,QAAQ,CAAEhD,IAAI,CAACgD,QAAQ,CACvBC,QAAQ,CAAEjD,IAAI,CAACiD,QAAQ,CACvBC,aAAa,CAAElD,IAAI,CAACkD,aAAa,CACjCC,OAAO,CAAEnD,IAAI,CAACmD,OAAO,CACrBC,OAAO,CAAEpD,IAAI,CAACoD,OAAO,EAAIxG,aAAe;AAC1C,CAAC,CACD;AACA,GAAI4F,KAAK,GAAK,OAAO,CAAE,CACrBK,UAAU,CAACC,cAAc,CAAGL,KAAK,CACnC,CAAC,IAAM,IAAID,KAAK,GAAK,UAAU,CAAE,CAC/BK,UAAU,CAACG,QAAQ,CAAGP,KAAK,CAC7B,CAAC,IAAM,IAAID,KAAK,GAAK,UAAU,CAAE,CAAE;AAChCK,UAAU,CAACI,QAAQ,CAAGR,KAAK,CAC9B,CAAC,IAAM,IAAID,KAAK,GAAK,MAAM,CAAE,CAC3B;AACAK,UAAU,CAACM,OAAO,CAAGV,KAAK,CAAGY,QAAQ,CAACZ,KAAK,CAAC,CAAG,IAAI,CACrD,CAAC,IAAM,IAAID,KAAK,GAAK,YAAY,CAAE,CACjC;AACAK,UAAU,CAACK,aAAa,CAAGT,KAAK,CAAGY,QAAQ,CAACZ,KAAK,CAAC,CAAG,IAAI,CACzD;AACAI,UAAU,CAACM,OAAO,CAAG,IAAI,CAC3B,CACAzF,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEkF,UAAU,CAAC,CAAE;AAChD,KAAM,CAAAhF,qBAAqB,CAAC,CAAEhB,EAAE,CAAEuF,MAAM,CAAE,GAAGS,UAAW,CAAC,CAAC,CAACS,MAAM,CAAC,CAAC,CACnE;AACA,KAAM,CAAArG,OAAO,CAAC,CAAC,CACf;AACA0D,gBAAgB,CAACD,OAAO,CAAE,CAAEtC,YAAY,CAAE,IAAK,CAAC,CAAC,CACjDpC,KAAK,CAACwE,OAAO,CAAC,uBAAuB,CAAC,CACxC,CAAE,MAAOoC,KAAK,CAAE,KAAAW,WAAA,CAAAC,kBAAA,CAAAC,qBAAA,CACd/F,OAAO,CAACkF,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC;AACA,GAAIA,KAAK,SAALA,KAAK,YAAAW,WAAA,CAALX,KAAK,CAAE9F,IAAI,UAAAyG,WAAA,YAAAC,kBAAA,CAAXD,WAAA,CAAaG,MAAM,UAAAF,kBAAA,YAAAC,qBAAA,CAAnBD,kBAAA,CAAqBV,cAAc,UAAAW,qBAAA,WAAnCA,qBAAA,CAAsC,CAAC,CAAC,CAAE,CAC5CzH,KAAK,CAAC4G,KAAK,CAACA,KAAK,CAAC9F,IAAI,CAAC4G,MAAM,CAACZ,cAAc,CAAC,CAAC,CAAC,CAAC,CAClD,CAAC,IAAM,KAAAa,YAAA,CACL3H,KAAK,CAAC4G,KAAK,CACT,CAAAA,KAAK,SAALA,KAAK,kBAAAe,YAAA,CAALf,KAAK,CAAE9F,IAAI,UAAA6G,YAAA,iBAAXA,YAAA,CAAaC,OAAO,GAAI,qCAC1B,CAAC,CACH,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAzB,MAAM,EAAK,CACrC3G,iBAAiB,CAAC,CAChBqI,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,CACF,KAAM,CAAAlG,qBAAqB,CAACwE,MAAM,CAAC,CAACkB,MAAM,CAAC,CAAC,CAC5C;AACA1D,MAAM,CAACC,IAAI,CAACpB,yBAAyB,CAAC,CAACY,OAAO,CAAEqB,OAAO,EAAK,CAC1DU,uBAAuB,CAACiC,QAAQ,CAAC3C,OAAO,CAAC,CAAE0B,MAAM,CAAC,CACpD,CAAC,CAAC,CACFnF,OAAO,CAAC,CAAC,CACTjB,KAAK,CAACwE,OAAO,CAAC,gCAAgC,CAAC,CACjD,CAAE,MAAOoC,KAAK,CAAE,CACdlF,OAAO,CAACkF,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD5G,KAAK,CAAC4G,KAAK,CAAC,8CAA8C,CAAC,CAC7D,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAmB,qBAAqB,CAAG,QAAAA,CAACrD,OAAO,CAA2B,IAAzB,CAAAsD,aAAa,CAAAC,SAAA,CAAAtE,MAAA,IAAAsE,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,IAAI,CAC1DhH,OAAO,CAAC,CAAC,CAACkH,IAAI,CAAC,IAAM,CACnB;AACA,GAAIH,aAAa,CAAE,CACjB9C,0BAA0B,CAACR,OAAO,CAAEsD,aAAa,CAAC,CACpD,CACF,CAAC,CAAC,CACFrD,gBAAgB,CAACD,OAAO,CAAE,CACxBvC,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,IAChB,CAAC,CAAC,CACF;AACApC,KAAK,CAACwE,OAAO,CAAC,8BAA8B,CAAC,CAC/C,CAAC,CAED;AACA,KAAM,CAAA4D,mBAAmB,CAAGA,CAAC1D,OAAO,CAAE0B,MAAM,GAAK,CAC/C,KAAM,CAAAC,YAAY,CAAG5B,aAAa,CAACC,OAAO,CAAC,CAC3C,KAAM,CAAA2D,gBAAgB,CAAGhC,YAAY,CAAC/D,cAAc,CACpD,KAAM,CAAAgG,YAAY,CAAGD,gBAAgB,CAACpD,QAAQ,CAACmB,MAAM,CAAC,CAClDiC,gBAAgB,CAACrD,MAAM,CAAEnE,EAAE,EAAKA,EAAE,GAAKuF,MAAM,CAAC,CAC9C,CAAC,GAAGiC,gBAAgB,CAAEjC,MAAM,CAAC,CACjCzB,gBAAgB,CAACD,OAAO,CAAE,CAAEpC,cAAc,CAAEgG,YAAa,CAAC,CAAC,CAC7D,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAI7D,OAAO,EAAK,CACtC,KAAM,CAAA2B,YAAY,CAAG5B,aAAa,CAACC,OAAO,CAAC,CAC3C,KAAM,CAAA8D,SAAS,CAAG,CAAAzH,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAED,IAAI,GAAI,EAAE,CACvC,GAAIuF,YAAY,CAAC/D,cAAc,CAACqB,MAAM,GAAK6E,SAAS,CAAC7E,MAAM,CAAE,CAC3DgB,gBAAgB,CAACD,OAAO,CAAE,CAAEpC,cAAc,CAAE,EAAG,CAAC,CAAC,CACnD,CAAC,IAAM,CACLqC,gBAAgB,CAACD,OAAO,CAAE,CACxBpC,cAAc,CAAEkG,SAAS,CAACzE,GAAG,CAAE0E,IAAI,EAAKA,IAAI,CAAC5H,EAAE,CACjD,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAA6H,WAAW,CAAG,KAAO,CAAAhE,OAAO,EAAK,CACrC,KAAM,CAAA2B,YAAY,CAAG5B,aAAa,CAACC,OAAO,CAAC,CAC3C,KAAM,CAAA8D,SAAS,CAAG3D,YAAY,CAACH,OAAO,CAAC,CACvC,KAAM,CAAAiE,aAAa,CAAGH,SAAS,CAACxD,MAAM,CAAEyD,IAAI,EAC1CpC,YAAY,CAAC/D,cAAc,CAAC2C,QAAQ,CAACwD,IAAI,CAAC5H,EAAE,CAC9C,CAAC,CAED,GAAI8H,aAAa,CAAChF,MAAM,GAAK,CAAC,CAAE,CAC9B3D,KAAK,CAAC4G,KAAK,CAAC,yCAAyC,CAAC,CACtD,OACF,CAEA;AACA,KAAM,CAAAgC,UAAU,CAAGD,aAAa,CAC7B5E,GAAG,CACD0E,IAAI,EACH,UAAUA,IAAI,CAAC3B,cAAc,EAAI2B,IAAI,CAAC1B,KAAK,IAAI,CAC/C,aAAa0B,IAAI,CAACzB,QAAQ,IAAI,CAC9B,aAAayB,IAAI,CAACxB,QAAQ,EAC9B,CAAC,CACA4B,IAAI,CAAC,IAAI,CAAC,CAEb;AACA,GAAI,CACF,KAAM,CAAAC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,UAAU,CAAC,CAC/C5I,KAAK,CAACwE,OAAO,CAAC,uBAAuBmE,aAAa,CAAChF,MAAM,uBAAuB,CAAC,CACjF;AACAgB,gBAAgB,CAACD,OAAO,CAAE,CAAEpC,cAAc,CAAE,EAAG,CAAC,CAAC,CACnD,CAAE,MAAOsE,KAAK,CAAE,CACdlF,OAAO,CAACkF,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD5G,KAAK,CAAC4G,KAAK,CAAC,wCAAwC,CAAC,CACvD,CACF,CAAC,CAED;AACA,KAAM,CAAAqC,wBAAwB,CAAG,KAAO,CAAAvE,OAAO,EAAK,CAClD,KAAM,CAAA2B,YAAY,CAAG5B,aAAa,CAACC,OAAO,CAAC,CAC3C,KAAM,CAAA8D,SAAS,CAAG3D,YAAY,CAACH,OAAO,CAAC,CAAE;AAEzC;AACA,GAAI8D,SAAS,CAAC7E,MAAM,GAAK,CAAC,CAAE,CAC1B,GAAI7B,MAAM,CAAC6B,MAAM,EAAI,CAAC,CAAE,CACtB3D,KAAK,CAAC4G,KAAK,CAAC,+DAA+D,CAAC,CAC5E,OACF,CAEAnH,iBAAiB,CAAC,CAChBsH,KAAK,CAAE,qBAAqB,CAC5BmC,IAAI,CAAE,oDAAoDzE,aAAa,CAACC,OAAO,CAAC,CAAClC,WAAW,kCAAkC,CAC9HsF,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,KAAAqB,cAAA,CACF;AACApH,SAAS,CAAEwC,IAAI,EAAKA,IAAI,CAACS,MAAM,CAAE1B,KAAK,EAAKA,KAAK,CAACzC,EAAE,GAAK6D,OAAO,CAAC,CAAC,CACjExC,cAAc,CAAEqC,IAAI,EAAK,CACvB,KAAM,CAAA6E,QAAQ,CAAG,CAAE,GAAG7E,IAAK,CAAC,CAC5B,MAAO,CAAA6E,QAAQ,CAAC1E,OAAO,CAAC,CACxB,MAAO,CAAA0E,QAAQ,CACjB,CAAC,CAAC,CACF1G,4BAA4B,CAAE6B,IAAI,EAAK,CACrC,KAAM,CAAA6E,QAAQ,CAAG,CAAE,GAAG7E,IAAK,CAAC,CAC5B,MAAO,CAAA6E,QAAQ,CAAC1E,OAAO,CAAC,CACxB,MAAO,CAAA0E,QAAQ,CACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAnI,OAAO,CAAC,CAAC,CAEfjB,KAAK,CAACwE,OAAO,CAAC,gBAAgB,EAAA2E,cAAA,CAAA1E,aAAa,CAACC,OAAO,CAAC,UAAAyE,cAAA,iBAAtBA,cAAA,CAAwB3G,WAAW,GAAI,SAAS,yBAAyB,CAAC,CAC1G,CAAE,MAAOoE,KAAK,CAAE,CACdlF,OAAO,CAACkF,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD5G,KAAK,CAAC4G,KAAK,CAAC,2CAA2C,CAAC,CAC1D,CACF,CACF,CAAC,CAAC,CACF,OACF,CAEA;AACA,GAAIP,YAAY,CAAC/D,cAAc,CAACqB,MAAM,GAAK,CAAC,CAAE,CAC5C3D,KAAK,CAAC4G,KAAK,CAAC,2CAA2C,CAAC,CACxD,OACF,CAEA,KAAM,CAAAyC,cAAc,CAAGhD,YAAY,CAAC/D,cAAc,CAAC0C,MAAM,CAAEoB,MAAM,EAAK,CACpE,KAAM,CAAAqC,IAAI,CAAGD,SAAS,CAAC7B,IAAI,CAAE2C,CAAC,EAAKA,CAAC,CAACzI,EAAE,GAAKuF,MAAM,CAAC,CACnD,MAAO,CAAAqC,IAAI,EAAIA,IAAI,CAACrB,OAAO,GAAKxG,aAAa,CAC/C,CAAC,CAAC,CAEF,GAAIyI,cAAc,CAAC1F,MAAM,GAAK,CAAC,CAAE,CAC/B3D,KAAK,CAAC4G,KAAK,CAAC,wDAAwD,CAAC,CACrE,OACF,CAEA;AACA,KAAM,CAAA2C,gBAAgB,CAAG7E,OAAO,CAAG,CAAC,CACpC,KAAM,CAAA8E,kBAAkB,CAAGhB,SAAS,CAAC7E,MAAM,CAAG,CAAC,EAAI0C,YAAY,CAAC/D,cAAc,CAACqB,MAAM,GAAK6E,SAAS,CAAC7E,MAAM,CAE1G,GAAI4F,gBAAgB,EAAIC,kBAAkB,CAAE,CAC1C;AACA,GAAI1H,MAAM,CAAC6B,MAAM,EAAI,CAAC,CAAE,CACtB;AACA;AACAjB,4BAA4B,CAAC6B,IAAI,GAAK,CACpC,GAAGA,IAAI,CACP,CAACG,OAAO,EAAG,EAAG;AAChB,CAAC,CAAC,CAAC,CACHC,gBAAgB,CAACD,OAAO,CAAE,CAAEpC,cAAc,CAAE,EAAG,CAAC,CAAC,CAAE;AACnD,KAAM,CAAArB,OAAO,CAAC,CAAC,CACfjB,KAAK,CAACwE,OAAO,CAAC,0CAA0C,CAAC,CACzD,OACF,CAEA/E,iBAAiB,CAAC,CAChBsH,KAAK,CAAE,sBAAsB,CAC7BmC,IAAI,CAAE,qDAAqDzE,aAAa,CAACC,OAAO,CAAC,CAAClC,WAAW,sDAAsD,CACnJsF,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,KAAA2B,eAAA,CACF;AACA,KAAM,CAAAjB,SAAS,CAAG3D,YAAY,CAACH,OAAO,CAAC,CACvC,KAAM,CAAAgF,gBAAgB,CAAGlB,SAAS,CAC/BxD,MAAM,CAACyD,IAAI,EAAIA,IAAI,CAACrB,OAAO,GAAKxG,aAAa,CAAC,CAC9CmD,GAAG,CAAC0E,IAAI,EAAIA,IAAI,CAAC5H,EAAE,CAAC,CAEvB,GAAI6I,gBAAgB,CAAC/F,MAAM,CAAG,CAAC,CAAE,CAC/B,KAAM,CAAAgG,OAAO,CAACC,GAAG,CACfF,gBAAgB,CAAC3F,GAAG,CAAEqC,MAAM,EAC1BxE,qBAAqB,CAACwE,MAAM,CAAC,CAACkB,MAAM,CAAC,CACvC,CACF,CAAC,CACH,CAEA;AACAvF,SAAS,CAAEwC,IAAI,EAAKA,IAAI,CAACS,MAAM,CAAE1B,KAAK,EAAKA,KAAK,CAACzC,EAAE,GAAK6D,OAAO,CAAC,CAAC,CACjExC,cAAc,CAAEqC,IAAI,EAAK,CACvB,KAAM,CAAA6E,QAAQ,CAAG,CAAE,GAAG7E,IAAK,CAAC,CAC5B,MAAO,CAAA6E,QAAQ,CAAC1E,OAAO,CAAC,CACxB,MAAO,CAAA0E,QAAQ,CACjB,CAAC,CAAC,CACF1G,4BAA4B,CAAE6B,IAAI,EAAK,CACrC,KAAM,CAAA6E,QAAQ,CAAG,CAAE,GAAG7E,IAAK,CAAC,CAC5B,MAAO,CAAA6E,QAAQ,CAAC1E,OAAO,CAAC,CACxB,MAAO,CAAA0E,QAAQ,CACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAnI,OAAO,CAAC,CAAC,CAEfjB,KAAK,CAACwE,OAAO,CAAC,UAAU,EAAAiF,eAAA,CAAAhF,aAAa,CAACC,OAAO,CAAC,UAAA+E,eAAA,iBAAtBA,eAAA,CAAwBjH,WAAW,GAAI,SAAS,yCAAyC,CAAC,CACpH,CAAE,MAAOoE,KAAK,CAAE,CACdlF,OAAO,CAACkF,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD5G,KAAK,CAAC4G,KAAK,CAAC,2DAA2D,CAAC,CAC1E,CACF,CACF,CAAC,CAAC,CACF,OACF,CAEA;AACAnH,iBAAiB,CAAC,CAChBqI,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,CACF,KAAM,CAAA6B,OAAO,CAACC,GAAG,CACfP,cAAc,CAACtF,GAAG,CAAEqC,MAAM,EACxBxE,qBAAqB,CAACwE,MAAM,CAAC,CAACkB,MAAM,CAAC,CACvC,CACF,CAAC,CACD;AACA+B,cAAc,CAAChG,OAAO,CAAE+C,MAAM,EAAK,CACjChB,uBAAuB,CAACV,OAAO,CAAE0B,MAAM,CAAC,CAC1C,CAAC,CAAC,CACFzB,gBAAgB,CAACD,OAAO,CAAE,CAAEpC,cAAc,CAAE,EAAG,CAAC,CAAC,CACjD,KAAM,CAAArB,OAAO,CAAC,CAAC,CACfjB,KAAK,CAACwE,OAAO,CACX,wBAAwB6E,cAAc,CAAC1F,MAAM,oCAC/C,CAAC,CACH,CAAE,MAAOiD,KAAK,CAAE,CACdlF,OAAO,CAACkF,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD5G,KAAK,CAAC4G,KAAK,CACT,2DACF,CAAC,CACH,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED,GAAI5F,SAAS,CAAE,CACb,mBACEd,IAAA,QAAK2J,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD5J,IAAA,QAAK2J,SAAS,CAAC,6DAA6D,CAAM,CAAC,CAChF,CAAC,CAEV,CAEA,mBACE3J,IAAA,QAAK2J,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAE5DhI,MAAM,CAACiC,GAAG,CAAET,KAAK,EAAK,CACrB,KAAM,CAAAyG,UAAU,CAAGtF,aAAa,CAACnB,KAAK,CAACzC,EAAE,CAAC,CAC1C,KAAM,CAAAmJ,WAAW,CAAG3E,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAmD,SAAS,CAAG3D,YAAY,CAACvB,KAAK,CAACzC,EAAE,CAAC,CACxC,mBACET,KAAA,QAAoByJ,SAAS,CAAC,cAAc,CAAAC,QAAA,eAE1C1J,KAAA,QAAKyJ,SAAS,CAAC,mGAAmG,CAAAC,QAAA,eAChH1J,KAAA,QAAKyJ,SAAS,CAAC,4GAA4G,CAAAC,QAAA,eAEzH1J,KAAA,QAAKyJ,SAAS,CAAC,2DAA2D,CAAAC,QAAA,EACvEC,UAAU,CAACxH,aAAa,cACvBrC,IAAA,UACE+J,IAAI,CAAC,MAAM,CACXxD,KAAK,CAAEsD,UAAU,CAACvH,WAAY,CAC9B0H,QAAQ,CAAGC,CAAC,EACVxF,gBAAgB,CAACrB,KAAK,CAACzC,EAAE,CAAE,CACzB2B,WAAW,CAAE2H,CAAC,CAACC,MAAM,CAAC3D,KACxB,CAAC,CACF,CACD4D,MAAM,CAAEA,CAAA,GACN1F,gBAAgB,CAACrB,KAAK,CAACzC,EAAE,CAAE,CAAE0B,aAAa,CAAE,KAAM,CAAC,CACpD,CACD+H,SAAS,CAAGH,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACI,GAAG,GAAK,OAAO,CAAE,CACrB5F,gBAAgB,CAACrB,KAAK,CAACzC,EAAE,CAAE,CAAE0B,aAAa,CAAE,KAAM,CAAC,CAAC,CACtD,CACF,CAAE,CACFsH,SAAS,CAAC,qLAAqL,CAC/LW,SAAS,MACV,CAAC,cAEFtK,IAAA,OACE2J,SAAS,CAAC,uGAAuG,CACjHY,OAAO,CAAEA,CAAA,GACP9F,gBAAgB,CAACrB,KAAK,CAACzC,EAAE,CAAE,CAAE0B,aAAa,CAAE,IAAK,CAAC,CACnD,CACDwE,KAAK,CAAC,0BAA0B,CAAA+C,QAAA,CAE/BC,UAAU,CAACvH,WAAW,CACrB,CACL,cAGDpC,KAAA,QAAKyJ,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAEvD5J,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAM/B,WAAW,CAACpF,KAAK,CAACzC,EAAE,CAAE,CACrCgJ,SAAS,CAAC,uLAAuL,CACjM9C,KAAK,CAAC,iCAAiC,CAAA+C,QAAA,cAEvC5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,OAEpD,CAAM,CAAC,CACD,CAAC,cAET5J,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMxB,wBAAwB,CAAC3F,KAAK,CAACzC,EAAE,CAAE,CAClDgJ,SAAS,CAAC,qLAAqL,CAC/L9C,KAAK,CAAC,kCAAkC,CAAA+C,QAAA,cAExC5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAEpD,CAAM,CAAC,CACD,CAAC,EACN,CAAC,EACH,CAAC,cAGN1J,KAAA,QAAKyJ,SAAS,CAAC,2CAA2C,CAAAC,QAAA,EACvDE,WAAW,CAAC9D,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACnC,GAAG,CAAC,CAACiC,MAAM,CAAE0E,KAAK,QAAAC,aAAA,CAAAC,aAAA,oBACzCxK,KAAA,QAAiByJ,SAAS,CAAC,UAAU,CAAAC,QAAA,EAClC9D,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEN,KAAK,cACZxF,IAAA,QACE2K,GAAG,CACD7E,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEN,KAAK,CACT,GAAGoF,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAIhF,MAAM,CAACN,KAAK,EAAE,CAC3D,qBACL,CACDuF,GAAG,CAAE,GAAGjF,MAAM,CAACR,KAAK,EAAI,MAAM,IAC5BQ,MAAM,CAACP,KAAK,EAAI,EAAE,EACjB,CACHoE,SAAS,CAAC,iFAAiF,CAC3FqB,OAAO,CAAGf,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACe,KAAK,CAACC,OAAO,CAAG,MAAM,CAC/BjB,CAAC,CAACC,MAAM,CAACiB,WAAW,CAACF,KAAK,CAACC,OAAO,CAAG,MAAM,CAC7C,CAAE,CACH,CAAC,CACA,IAAI,cAERhL,KAAA,QACEyJ,SAAS,CAAC,+IAA+I,CACzJsB,KAAK,CAAE,CAAEC,OAAO,CAAEpF,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEN,KAAK,CAAG,MAAM,CAAG,MAAO,CAAE,CAAAoE,QAAA,EAEnD,CAAA9D,MAAM,SAANA,MAAM,kBAAA2E,aAAA,CAAN3E,MAAM,CAAER,KAAK,UAAAmF,aAAA,iBAAbA,aAAA,CAAeW,MAAM,CAAC,CAAC,CAAC,GAAI,GAAG,CAC/B,CAAAtF,MAAM,SAANA,MAAM,kBAAA4E,aAAA,CAAN5E,MAAM,CAAEP,KAAK,UAAAmF,aAAA,iBAAbA,aAAA,CAAeU,MAAM,CAAC,CAAC,CAAC,GAAI,GAAG,EAC7B,CAAC,GAzBEZ,KA0BL,CAAC,EACP,CAAC,CACDV,WAAW,CAACrG,MAAM,CAAG,CAAC,eACrBvD,KAAA,QAAKyJ,SAAS,CAAC,mJAAmJ,CAAAC,QAAA,EAAC,GAChK,CAACE,WAAW,CAACrG,MAAM,CAAG,CAAC,EACrB,CACN,EACE,CAAC,EACH,CAAC,cAGNvD,KAAA,WACEqK,OAAO,CAAEA,CAAA,GAAM,CACb9F,gBAAgB,CAACrB,KAAK,CAACzC,EAAE,CAAE,CACzBuB,YAAY,CAAE,IAAI,CAClBD,WAAW,CAAE,CAAC4H,UAAU,CAAC5H,WAC3B,CAAC,CAAC,CACJ,CAAE,CACF0H,SAAS,CAAC,2dAA2d,CAAAC,QAAA,eAEre5J,IAAA,QACE2J,SAAS,CAAC,gIAAgI,CAC1I0B,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,OAAO,CACdC,OAAO,CAAC,WAAW,CAAA3B,QAAA,cAEnB5J,IAAA,SACEwL,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,WAAW,CAAE,CAAE,CACfC,CAAC,CAAC,4BAA4B,CAC/B,CAAC,CACC,CAAC,eAER,EAAQ,CAAC,EACN,CAAC,CAGL9B,UAAU,CAAC5H,WAAW,eACrBjC,IAAA,QAAK2J,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnF5J,IAAA,CAACV,mBAAmB,EAClBsM,QAAQ,CAAEA,CAAA,GAAM,CACdnH,gBAAgB,CAACrB,KAAK,CAACzC,EAAE,CAAE,CACzBsB,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,IAChB,CAAC,CAAC,CACJ,CAAE,CACF2J,SAAS,CAAG/D,aAAa,EACvBD,qBAAqB,CAACzE,KAAK,CAACzC,EAAE,CAAEmH,aAAa,CAC9C,CACDvH,iBAAiB,CAAEA,iBAAkB,CACrCC,gBAAgB,CAAEA,gBAAiB,CACnCsL,QAAQ,CACNjC,UAAU,CAAC3H,YAAY,CACnBoG,SAAS,CAAC7B,IAAI,CAAE3C,IAAI,EAAKA,IAAI,CAACnD,EAAE,GAAKkJ,UAAU,CAAC3H,YAAY,CAAC,CAC7D,IACL,CACDb,eAAe,CAAEA,eAAgB,CACjCC,SAAS,CAAEA,SAAU,CACtB,CAAC,CACC,CACN,cAGDtB,IAAA,QAAK2J,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACzE5J,IAAA,QAAK2J,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BtB,SAAS,CAAC7E,MAAM,GAAK,CAAC,cACrBzD,IAAA,QAAK2J,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C5J,IAAA,MAAG2J,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,6EAGvD,CAAG,CAAC,CACD,CAAC,cAEN1J,KAAA,UAAOyJ,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAClE5J,IAAA,UAAO2J,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cACpD1J,KAAA,OAAA0J,QAAA,eACE5J,IAAA,OAAI+L,KAAK,CAAC,KAAK,CAACpC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cAC/D5J,IAAA,UACE+J,IAAI,CAAC,UAAU,CACfiC,OAAO,CACLnC,UAAU,CAACzH,cAAc,CAACqB,MAAM,GAC9B6E,SAAS,CAAC7E,MAAM,EAAI6E,SAAS,CAAC7E,MAAM,CAAG,CAC1C,CACDuG,QAAQ,CAAEA,CAAA,GAAM3B,kBAAkB,CAACjF,KAAK,CAACzC,EAAE,CAAE,CAC7CgJ,SAAS,CAAC,6FAA6F,CACxG,CAAC,CACA,CAAC,cACL3J,IAAA,OACE+L,KAAK,CAAC,KAAK,CACXpC,SAAS,CAAC,+HAA+H,CAAAC,QAAA,cAEzI1J,KAAA,QAAKyJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,OAEjC,cAAA5J,IAAA,QACE2J,SAAS,CAAC,4BAA4B,CACtC0B,IAAI,CAAC,cAAc,CACnBE,OAAO,CAAC,WAAW,CAAA3B,QAAA,cAEnB5J,IAAA,SACEiM,QAAQ,CAAC,SAAS,CAClBN,CAAC,CAAC,oHAAoH,CACtHO,QAAQ,CAAC,SAAS,CACnB,CAAC,CACC,CAAC,EACH,CAAC,CACJ,CAAC,cACLlM,IAAA,OACE+L,KAAK,CAAC,KAAK,CACXpC,SAAS,CAAC,+HAA+H,CAAAC,QAAA,cAEzI1J,KAAA,QAAKyJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,WAEjC,cAAA5J,IAAA,QACE2J,SAAS,CAAC,4BAA4B,CACtC0B,IAAI,CAAC,cAAc,CACnBE,OAAO,CAAC,WAAW,CAAA3B,QAAA,cAEnB5J,IAAA,SACEiM,QAAQ,CAAC,SAAS,CAClBN,CAAC,CAAC,oHAAoH,CACtHO,QAAQ,CAAC,SAAS,CACnB,CAAC,CACC,CAAC,EACH,CAAC,CACJ,CAAC,cACLlM,IAAA,OACE+L,KAAK,CAAC,KAAK,CACXpC,SAAS,CAAC,+HAA+H,CAAAC,QAAA,CAC1I,UAED,CAAI,CAAC,cACL5J,IAAA,OACE+L,KAAK,CAAC,KAAK,CACXpC,SAAS,CAAC,+HAA+H,CAAAC,QAAA,CAC1I,MAED,CAAI,CAAC,cACL5J,IAAA,OACE+L,KAAK,CAAC,KAAK,CACXpC,SAAS,CAAC,+HAA+H,CAAAC,QAAA,CAC1I,YAED,CAAI,CAAC,cACL5J,IAAA,OACE+L,KAAK,CAAC,KAAK,CACXpC,SAAS,CAAC,8HAA8H,CAAAC,QAAA,CACzI,OAED,CAAI,CAAC,cACL5J,IAAA,OACE+L,KAAK,CAAC,KAAK,CACXpC,SAAS,CAAC,8HAA8H,CAAAC,QAAA,CACzI,QAED,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR5J,IAAA,UAAO2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDtB,SAAS,CAACzE,GAAG,CAAE0E,IAAI,eAClBvI,IAAA,CAACmM,QAAQ,EAEP5D,IAAI,CAAEA,IAAK,CACX/D,OAAO,CAAEpB,KAAK,CAACzC,EAAG,CAClByL,SAAS,CAAEvC,UAAU,CAAC3H,YAAY,GAAKqG,IAAI,CAAC5H,EAAG,CAC/C0L,WAAW,CAAExC,UAAU,CAACzH,cAAc,CAAC2C,QAAQ,CAC7CwD,IAAI,CAAC5H,EACP,CAAE,CACF2L,eAAe,CAAEzC,UAAU,CAAC1H,gBAAgB,CAACoG,IAAI,CAAC5H,EAAE,CAAE,CACtDD,aAAa,CAAEA,aACf;AAAA,CACAW,eAAe,CAAEA,eAAgB,CACjCC,SAAS,CAAEA,SAAU,CACrBiL,MAAM,CAAEnG,UAAW,CACnBoG,QAAQ,CAAE7E,YAAa,CACvB8E,gBAAgB,CAAExG,wBAAyB,CAC3CyG,iBAAiB,CAAExE,mBAAoB,CACvCyE,gBAAgB,CAAEtG,oBAAqB,EAhBlCkC,IAAI,CAAC5H,EAiBX,CACF,CAAC,CACG,CAAC,EACH,CACR,CACE,CAAC,CACH,CAAC,cAGNX,IAAA,QAAK2J,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/C1J,KAAA,WACEqK,OAAO,CAAExG,iBAAkB,CAC3B4F,SAAS,CAAC,yMAAyM,CAAAC,QAAA,eAGnN5J,IAAA,QAAK2J,SAAS,CAAC,yGAAyG,CAAAC,QAAA,cACtH5J,IAAA,SAAM2J,SAAS,CAAC,kEAAkE,CAAAC,QAAA,CAAC,GAEnF,CAAM,CAAC,CACJ,CAAC,cAEN5J,IAAA,SAAM2J,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,uBAEjD,CAAM,CAAC,EACD,CAAC,CACN,CAAC,GAvSExG,KAAK,CAACzC,EAwSX,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAwL,QAAQ,CAAGS,KAAA,EAcX,KAAAC,KAAA,CAAAC,YAAA,CAAAC,UAAA,CAAAC,gBAAA,IAdY,CAChBzE,IAAI,CACJ/D,OAAO,CACP4H,SAAS,CACTC,WAAW,CACXC,eAAe,CACf5L,aAAa,CACbW,eAAe,CAAG,EAAE,CACpBC,SAAS,CAAG,EAAE,CACdiL,MAAM,CACNC,QAAQ,CACRC,gBAAgB,CAChBC,iBAAiB,CACjBC,gBACF,CAAC,CAAAC,KAAA,CACC;AACA,KAAM,CAACK,UAAU,CAAEC,aAAa,CAAC,CAAG9N,QAAQ,CAAC,CAC3CyH,KAAK,CAAE0B,IAAI,CAAC3B,cAAc,EAAI2B,IAAI,CAAC1B,KAAK,EAAI0B,IAAI,CAAC4E,SAAS,EAAI,EAAE,CAChErG,QAAQ,CAAEyB,IAAI,CAACzB,QAAQ,EAAIyB,IAAI,CAAC4E,SAAS,EAAI,EAAE,CAC/CpG,QAAQ,CAAEwB,IAAI,CAACxB,QAAQ,EAAI,EAAE,CAC7BE,OAAO,CAAEsB,IAAI,CAACtB,OAAO,CAAGmG,MAAM,CAAC7E,IAAI,CAACtB,OAAO,CAAC,CAAG,EAAE,CACjDD,aAAa,CAAEuB,IAAI,CAACvB,aAAa,CAAGoG,MAAM,CAAC7E,IAAI,CAACvB,aAAa,CAAC,CAAG,EACnE,CAAC,CAAC,CAEF;AACA3H,SAAS,CAAC,IAAM,CACd6N,aAAa,CAAC,CACZrG,KAAK,CAAE0B,IAAI,CAAC3B,cAAc,EAAI2B,IAAI,CAAC1B,KAAK,EAAI0B,IAAI,CAAC4E,SAAS,EAAI,EAAE,CAChErG,QAAQ,CAAEyB,IAAI,CAACzB,QAAQ,EAAIyB,IAAI,CAAC4E,SAAS,EAAI,EAAE,CAC/CpG,QAAQ,CAAEwB,IAAI,CAACxB,QAAQ,EAAI,EAAE,CAC7BE,OAAO,CAAEsB,IAAI,CAACtB,OAAO,CAAGmG,MAAM,CAAC7E,IAAI,CAACtB,OAAO,CAAC,CAAG,EAAE,CACjDD,aAAa,CAAEuB,IAAI,CAACvB,aAAa,CAAGoG,MAAM,CAAC7E,IAAI,CAACvB,aAAa,CAAC,CAAG,EACnE,CAAC,CAAC,CACJ,CAAC,CAAE,CAACuB,IAAI,CAAC,CAAC,CAEV;AACA,KAAM,CAAA8E,iBAAiB,CAAGA,CAAC/G,KAAK,CAAEC,KAAK,GAAK,CAC1C,GAAID,KAAK,GAAK,eAAe,CAAE,CAC7B4G,aAAa,CAAE7I,IAAI,GAAM,CACvB,GAAGA,IAAI,CACP,CAACiC,KAAK,EAAGC,KAAK,CACdU,OAAO,CAAE,EACX,CAAC,CAAC,CAAC,CACL,CAAC,IAAM,CACLiG,aAAa,CAAE7I,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAE,CAACiC,KAAK,EAAGC,KAAM,CAAC,CAAC,CAAC,CACxD,CACF,CAAC,CAED;AACA,KAAM,CAAA+G,eAAe,CAAIhH,KAAK,EAAK,CACjC,GAAI,CAAAiH,aAAa,CACjB,GAAIjH,KAAK,GAAK,OAAO,CAAE,CACrBiH,aAAa,CAAGhF,IAAI,CAAC3B,cAAc,EAAI2B,IAAI,CAAC1B,KAAK,EAAI0B,IAAI,CAAC4E,SAAS,CACrE,CAAC,IAAM,IAAI7G,KAAK,GAAK,UAAU,CAAE,CAC/BiH,aAAa,CAAGhF,IAAI,CAACzB,QAAQ,EAAIyB,IAAI,CAAC4E,SAAS,CACjD,CAAC,IAAM,IAAI7G,KAAK,GAAK,UAAU,CAAE,CAC9BiH,aAAa,CAAGhF,IAAI,CAACxB,QAAQ,CAChC,CAAC,IAAM,IAAIT,KAAK,GAAK,SAAS,CAAE,CAC9BiH,aAAa,CAAGhF,IAAI,CAACtB,OAAO,CAAGmG,MAAM,CAAC7E,IAAI,CAACtB,OAAO,CAAC,CAAG,EAAE,CAC1D,CAAC,IAAM,IAAIX,KAAK,GAAK,eAAe,CAAE,CACpCiH,aAAa,CAAGhF,IAAI,CAACvB,aAAa,CAAGoG,MAAM,CAAC7E,IAAI,CAACvB,aAAa,CAAC,CAAG,EAAE,CACtE,CACA;AACA,KAAM,CAAAwG,cAAc,CAAG,CAAC,OAAO,CAAE,UAAU,CAAE,UAAU,CAAE,eAAe,CAAE,SAAS,CAAC,CACpF,GAAIA,cAAc,CAACzI,QAAQ,CAACuB,KAAK,CAAC,EAAI,CAAC2G,UAAU,CAAC3G,KAAK,CAAC,CAAE,CACxDxG,KAAK,CAAC4G,KAAK,CAAC,OAAOJ,KAAK,CAACmH,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,qBAAqB,CAAC,CAChE,OACF,CACA,GAAIR,UAAU,CAAC3G,KAAK,CAAC,GAAKiH,aAAa,CAAE,CACvC,KAAM,CAAAG,YAAY,CAChBpH,KAAK,GAAK,SAAS,CACf,MAAM,CACNA,KAAK,GAAK,eAAe,CACzB,YAAY,CACZA,KAAK,CACXqG,gBAAgB,CAACnI,OAAO,CAAE+D,IAAI,CAAC5H,EAAE,CAAE+M,YAAY,CAAET,UAAU,CAAC3G,KAAK,CAAC,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAqH,aAAa,CAAI1D,CAAC,EAAK,CAC3B,GAAIA,CAAC,CAACI,GAAG,GAAK,OAAO,CAAE,CACrBJ,CAAC,CAACC,MAAM,CAAC0D,IAAI,CAAC,CAAC,CACjB,CACF,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIC,KAAK,EAAK,CAC1C,OAAQA,KAAK,EACX,IAAK,QAAQ,CACX,MAAO,4BAA4B,CACrC,IAAK,UAAU,CACb,MAAO,6BAA6B,CACtC,IAAK,MAAM,CACT,MAAO,0BAA0B,CACnC,QACE,MAAO,qDAAqD,CAChE,CACF,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI,CAACd,UAAU,CAACjG,aAAa,EAAI,CAACpB,KAAK,CAACC,OAAO,CAACvE,SAAS,CAAC,CAAE,MAAO,EAAE,CACrE,MAAO,CAAAA,SAAS,CAACwD,MAAM,CACpBY,IAAI,EACHA,IAAI,CAACsI,cAAc,EACnBtI,IAAI,CAACsI,cAAc,CAACjJ,QAAQ,CAACoC,QAAQ,CAAC8F,UAAU,CAACjG,aAAa,CAAC,CACnE,CAAC,CACH,CAAC,CAED,mBACE9G,KAAA,OAAIyJ,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAEhD5J,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACvC5J,IAAA,UACE+J,IAAI,CAAC,UAAU,CACfiC,OAAO,CAAEK,WAAY,CACrBrC,QAAQ,CAAEA,CAAA,GAAM0C,iBAAiB,CAAClI,OAAO,CAAE+D,IAAI,CAAC5H,EAAE,CAAE,CACpDgJ,SAAS,CAAC,6FAA6F,CACxG,CAAC,CACA,CAAC,cAGL3J,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACvC1J,KAAA,QAAKyJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAEhC5J,IAAA,QAAK2J,SAAS,CAAC,uJAAuJ,CAAAC,QAAA,CACnK,EAAAiD,KAAA,CAACtE,IAAI,CAAC3B,cAAc,EAAI2B,IAAI,CAAC1B,KAAK,EAAI0B,IAAI,CAAC4E,SAAS,UAAAN,KAAA,kBAAAC,YAAA,CAApDD,KAAA,CACGzB,MAAM,CAAC,CAAC,CAAC,UAAA0B,YAAA,iBADZA,YAAA,CAEGmB,WAAW,CAAC,CAAC,GAAI,GAAG,CACrB,CAAC,CACL7B,SAAS,cACRpM,IAAA,UACE+J,IAAI,CAAC,MAAM,CACXxD,KAAK,CAAE0G,UAAU,CAACpG,KAAM,CACxBmD,QAAQ,CAAGC,CAAC,EAAKoD,iBAAiB,CAAC,OAAO,CAAEpD,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE,CAC5D4D,MAAM,CAAEA,CAAA,GAAMmD,eAAe,CAAC,OAAO,CAAE,CACvClD,SAAS,CAAEuD,aAAc,CACzBhE,SAAS,CAAC,oJAAoJ,CAC9JW,SAAS,MACT4D,WAAW,CAAC,gBAAgB,CAC7B,CAAC,cAEFlO,IAAA,SAAM2J,SAAS,CAAC,0DAA0D,CAAAC,QAAA,CACvErB,IAAI,CAAC3B,cAAc,EAAI2B,IAAI,CAAC1B,KAAK,EAAI0B,IAAI,CAAC4E,SAAS,CAChD,CACP,EACE,CAAC,CACJ,CAAC,cAGLnN,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACvC1J,KAAA,QAAKyJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/BwC,SAAS,cACRpM,IAAA,UACE+J,IAAI,CAAC,MAAM,CACXxD,KAAK,CAAE0G,UAAU,CAACnG,QAAS,CAC3BkD,QAAQ,CAAGC,CAAC,EAAKoD,iBAAiB,CAAC,UAAU,CAAEpD,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE,CAC/D4D,MAAM,CAAEA,CAAA,GAAMmD,eAAe,CAAC,UAAU,CAAE,CAC1ClD,SAAS,CAAEuD,aAAc,CACzBhE,SAAS,CAAC,qJAAqJ,CAC/JuE,WAAW,CAAC,mBAAmB,CAChC,CAAC,cAEFlO,IAAA,SAAM2J,SAAS,CAAC,yDAAyD,CAAAC,QAAA,CACtErB,IAAI,CAACzB,QAAQ,EAAIyB,IAAI,CAAC4E,SAAS,CAC5B,CACP,cAEDnN,IAAA,WACE2J,SAAS,CAAC,0LAA0L,CACpM9C,KAAK,CAAC,eAAe,CACrB0D,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAzD,QAAQ,CAAGyB,IAAI,CAACzB,QAAQ,EAAIyB,IAAI,CAAC4E,SAAS,CAChD,GAAIrG,QAAQ,CAAE,CACZ8B,SAAS,CAACC,SAAS,CAACC,SAAS,CAAChC,QAAQ,CAAC,CACvChH,KAAK,CAACwE,OAAO,CAAC,+BAA+B,CAAC,CAChD,CACF,CAAE,CAAAsF,QAAA,cAEF5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,cAEpD,CAAM,CAAC,CACD,CAAC,EACN,CAAC,CACJ,CAAC,cAGL5J,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACvC1J,KAAA,QAAKyJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/BwC,SAAS,cACRlM,KAAA,CAAAE,SAAA,EAAAwJ,QAAA,eACE5J,IAAA,UACE+J,IAAI,CAAEuC,eAAe,CAAG,MAAM,CAAG,UAAW,CAC5C/F,KAAK,CAAE0G,UAAU,CAAClG,QAAS,CAC3BiD,QAAQ,CAAGC,CAAC,EAAKoD,iBAAiB,CAAC,UAAU,CAAEpD,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE,CAC/D4D,MAAM,CAAEA,CAAA,GAAMmD,eAAe,CAAC,UAAU,CAAE,CAC1ClD,SAAS,CAAEuD,aAAc,CACzBhE,SAAS,CAAC,uJAAuJ,CACjKuE,WAAW,CAAC,UAAU,CACvB,CAAC,cACFlO,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMkC,gBAAgB,CAACjI,OAAO,CAAE+D,IAAI,CAAC5H,EAAE,CAAE,CAClDgJ,SAAS,CAAC,yLAAyL,CACnM9C,KAAK,CAAEyF,eAAe,CAAG,eAAe,CAAG,eAAgB,CAC3DvC,IAAI,CAAC,QAAQ,CAAAH,QAAA,cAEb5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChD0C,eAAe,CAAG,gBAAgB,CAAG,YAAY,CAC9C,CAAC,CACD,CAAC,EACT,CAAC,cAEHpM,KAAA,CAAAE,SAAA,EAAAwJ,QAAA,eACE5J,IAAA,SAAM2J,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CACxE0C,eAAe,CAAG/D,IAAI,CAACxB,QAAQ,CAAG,cAAc,CAC7C,CAAC,cACP/G,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMkC,gBAAgB,CAACjI,OAAO,CAAE+D,IAAI,CAAC5H,EAAE,CAAE,CAClDgJ,SAAS,CAAC,yLAAyL,CACnM9C,KAAK,CAAEyF,eAAe,CAAG,eAAe,CAAG,eAAgB,CAC3DvC,IAAI,CAAC,QAAQ,CAAAH,QAAA,cAEb5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChD0C,eAAe,CAAG,gBAAgB,CAAG,YAAY,CAC9C,CAAC,CACD,CAAC,EACT,CACH,cAEDtM,IAAA,WACE2J,SAAS,CAAC,kLAAkL,CAC5L9C,KAAK,CAAC,eAAe,CACrB0D,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAA4D,cAAc,CAAG/B,SAAS,CAC5Ba,UAAU,CAAClG,QAAQ,CACnBwB,IAAI,CAACxB,QAAQ,CACjB,GAAIoH,cAAc,CAAE,CAClBvF,SAAS,CAACC,SAAS,CAACC,SAAS,CAACqF,cAAc,CAAC,CAC7CrO,KAAK,CAACwE,OAAO,CAAC,+BAA+B,CAAC,CAChD,CACF,CAAE,CACFyF,IAAI,CAAC,QAAQ,CAAAH,QAAA,cAEb5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,cAEpD,CAAM,CAAC,CACD,CAAC,EACN,CAAC,CACJ,CAAC,cAGL5J,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACtCwC,SAAS,cACRlM,KAAA,WACEqG,KAAK,CAAE0G,UAAU,CAAChG,OAAQ,CAC1B+C,QAAQ,CAAGC,CAAC,EAAKoD,iBAAiB,CAAC,SAAS,CAAEpD,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE,CAC9D4D,MAAM,CAAEA,CAAA,GAAMmD,eAAe,CAAC,SAAS,CAAE,CACzCc,QAAQ,CAAE,CAACnB,UAAU,CAACjG,aAAc,CACpC2C,SAAS,CAAE,2IACT,CAACsD,UAAU,CAACjG,aAAa,CAAG,gCAAgC,CAAG,EAAE,EAChE,CAAA4C,QAAA,eAEH5J,IAAA,WAAQuG,KAAK,CAAC,EAAE,CAAAqD,QAAA,CAAC,aAAW,CAAQ,CAAC,CACpCmE,gBAAgB,CAAC,CAAC,CAAClK,GAAG,CAAE6B,IAAI,eAC3B1F,IAAA,WAAsBuG,KAAK,CAAEb,IAAI,CAAC/E,EAAG,CAAAiJ,QAAA,CAClClE,IAAI,CAAC5D,IAAI,EADC4D,IAAI,CAAC/E,EAEV,CACT,CAAC,EACI,CAAC,cAETX,IAAA,SAAM2J,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAC3D,EAAAmD,UAAA,CAAAxE,IAAI,CAAC7C,IAAI,UAAAqH,UAAA,iBAATA,UAAA,CAAWjL,IAAI,GAAI,WAAW,CAC3B,CACP,CACC,CAAC,cAGL9B,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACtCwC,SAAS,cACRlM,KAAA,WACEqG,KAAK,CAAE0G,UAAU,CAACjG,aAAa,EAAI,EAAG,CACtCgD,QAAQ,CAAGC,CAAC,EAAKoD,iBAAiB,CAAC,eAAe,CAAEpD,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE,CACpE4D,MAAM,CAAEA,CAAA,GAAMmD,eAAe,CAAC,eAAe,CAAE,CAC/C3D,SAAS,CAAC,yIAAyI,CAAAC,QAAA,eAEnJ5J,IAAA,WAAQuG,KAAK,CAAC,EAAE,CAAAqD,QAAA,CAAC,mBAAiB,CAAQ,CAAC,CAC1CvI,eAAe,CAACwC,GAAG,CAAEwK,IAAI,eACxBrO,IAAA,WAAsBuG,KAAK,CAAE8H,IAAI,CAAC1N,EAAG,CAAAiJ,QAAA,CAClCyE,IAAI,CAACvM,IAAI,EADCuM,IAAI,CAAC1N,EAEV,CACT,CAAC,EACI,CAAC,cAETX,IAAA,SAAM2J,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAC3D,EAAAoD,gBAAA,CAAAzE,IAAI,CAAC+F,UAAU,UAAAtB,gBAAA,iBAAfA,gBAAA,CAAiBlL,IAAI,GAAI,iBAAiB,CACvC,CACP,CACC,CAAC,cAGL9B,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACvC5J,IAAA,SACE2J,SAAS,CAAE,yCAAyCkE,wBAAwB,CAC1EtF,IAAI,CAACuF,KACP,CAAC,EAAG,CAAAlE,QAAA,CAEHrB,IAAI,CAACuF,KAAK,EAAI,iBAAiB,CAC5B,CAAC,CACL,CAAC,cAGL9N,IAAA,OAAI2J,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACvC1J,KAAA,QAAKyJ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAE1C5J,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMgC,MAAM,CAAC/H,OAAO,CAAE+D,IAAI,CAAC5H,EAAE,CAAE,CACxCgJ,SAAS,CAAC,4KAA4K,CACtL9C,KAAK,CAAC,MAAM,CAAA+C,QAAA,cAEZ5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,aAEpD,CAAM,CAAC,CACD,CAAC,CAERrB,IAAI,CAACrB,OAAO,GAAKxG,aAAa,eAC7BV,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMiC,QAAQ,CAACjE,IAAI,CAAC5H,EAAE,CAAE,CACjCgJ,SAAS,CAAC,0KAA0K,CACpL9C,KAAK,CAAC,QAAQ,CAAA+C,QAAA,cAEd5J,IAAA,SAAM2J,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAEpD,CAAM,CAAC,CACD,CACT,EACE,CAAC,CACJ,CAAC,EACH,CAAC,CAET,CAAC,CAED,cAAe,CAAAvJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}