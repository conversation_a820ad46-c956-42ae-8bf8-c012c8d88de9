{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\AddPasswordCardForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Eye, EyeOff, X, Shield, User, Lock, Building, Users, AlertCircle, CheckCircle, Wand2 } from \"lucide-react\";\nimport { useCreatePasswordManagerMutation, useUpdatePasswordManagerMutation } from \"../../features/api/passwordManagerApi\";\nimport { useGetDepartmentsWithTeamsQuery } from \"../../features/api/departmentApi\";\nimport { useGetTeamsWithDepartmentsQuery } from \"../../features/api/teamApi\";\n\n// Main component for the Add Password Card form\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddPasswordCardForm = ({\n  onCancel,\n  generatedPassword,\n  passwordStrength,\n  editData = null,\n  // For editing existing password\n  onSuccess,\n  // Callback after successful save\n  departmentsData: propDepartmentsData = null,\n  teamsData: propTeamsData = null\n}) => {\n  _s();\n  // Use props if provided, otherwise fallback to API hooks\n  const {\n    data: departmentsDataApi,\n    isLoading: isDepartmentsLoading,\n    error: departmentsError\n  } = useGetDepartmentsWithTeamsQuery(undefined, {\n    skip: !!propDepartmentsData\n  });\n  const {\n    data: teamsDataApi,\n    isLoading: isTeamsLoading,\n    error: teamsError\n  } = useGetTeamsWithDepartmentsQuery(undefined, {\n    skip: !!propTeamsData\n  });\n  const departmentsData = propDepartmentsData || departmentsDataApi;\n  const teamsData = propTeamsData || teamsDataApi;\n  const [createPasswordManager, {\n    isLoading: isCreating\n  }] = useCreatePasswordManagerMutation();\n  const [updatePasswordManager, {\n    isLoading: isUpdating\n  }] = useUpdatePasswordManagerMutation();\n  // console.clear();\n\n  // console.log(departmentsData);\n\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    password_title: \"\",\n    username: \"\",\n    password: \"\",\n    department_id: \"\",\n    team_id: \"\"\n  });\n\n  // Initialize form data for editing or reset for new entries\n  useEffect(() => {\n    if (editData) {\n      setFormData({\n        password_title: editData.password_title || editData.title || \"\",\n        username: editData.username || \"\",\n        password: \"\",\n        // Don't pre-fill password for security\n        department_id: editData.department_id ? String(editData.department_id) : \"\",\n        team_id: editData.team_id ? String(editData.team_id) : \"\"\n      });\n    } else {\n      // FIXED: Reset form completely when not editing (new entry)\n      setFormData({\n        password_title: \"\",\n        username: \"\",\n        password: \"\",\n        department_id: \"\",\n        team_id: \"\"\n      });\n      setErrors({});\n      setFormError(\"\");\n      setShowPassword(false);\n    }\n  }, [editData]);\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [focusedField, setFocusedField] = useState(null);\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === \"department_id\") {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n        team_id: \"\"\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: \"\"\n      }));\n    }\n\n    // Clear form error when user starts typing\n    if (formError) {\n      setFormError(\"\");\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = password => {\n    if (!password) return \"Weak Password\";\n    let score = 0;\n    if (password.length >= 12) score += 2;else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return \"Strong Password\";\n    if (score >= 4) return \"Moderate Password\";\n    return \"Weak Password\";\n  };\n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = strength => {\n    switch (strength) {\n      case \"Strong Password\":\n        return \"bg-emerald-50 text-emerald-700 border-emerald-200\";\n      case \"Moderate Password\":\n        return \"bg-amber-50 text-amber-700 border-amber-200\";\n      case \"Weak Password\":\n        return \"bg-red-50 text-red-700 border-red-200\";\n      default:\n        return \"bg-gray-50 text-gray-600 border-gray-200\";\n    }\n  };\n\n  // Get strength progress and color\n  const getStrengthProgress = strength => {\n    switch (strength) {\n      case \"Strong Password\":\n        return {\n          width: \"100%\",\n          color: \"bg-emerald-500\"\n        };\n      case \"Moderate Password\":\n        return {\n          width: \"66%\",\n          color: \"bg-amber-500\"\n        };\n      case \"Weak Password\":\n        return {\n          width: \"33%\",\n          color: \"bg-red-500\"\n        };\n      default:\n        return {\n          width: \"0%\",\n          color: \"bg-gray-300\"\n        };\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.password_title.trim()) newErrors.password_title = \"Platform title is required\";\n    if (!formData.username.trim()) newErrors.username = \"Username is required\";\n    if (!formData.password.trim()) newErrors.password = \"Password is required\";\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setFormError(\"\");\n    setIsLoading(true);\n    if (validateForm()) {\n      try {\n        var _result, _result$data;\n        const submitData = {\n          password_title: formData.password_title,\n          username: formData.username,\n          password: formData.password,\n          department_id: formData.department_id ? parseInt(formData.department_id) : null,\n          team_id: formData.team_id ? parseInt(formData.team_id) : null\n        };\n        let newPasswordId = null;\n        let result;\n        if (editData) {\n          // Update existing password\n          result = await updatePasswordManager({\n            id: editData.id,\n            ...submitData\n          }).unwrap();\n        } else {\n          // Create new password\n          result = await createPasswordManager(submitData).unwrap();\n        }\n\n        // Pass the new or updated item's ID to the success handler\n        newPasswordId = (_result = result) === null || _result === void 0 ? void 0 : (_result$data = _result.data) === null || _result$data === void 0 ? void 0 : _result$data.id;\n\n        // FIXED: Reset form after successful submission\n        setFormData({\n          password_title: \"\",\n          username: \"\",\n          password: \"\",\n          department_id: \"\",\n          team_id: \"\"\n        });\n        setErrors({});\n        setFormError(\"\");\n        setShowPassword(false);\n\n        // FIXED: Call success callback to refresh data and close modal\n        // The success callback will handle the toast notification\n        if (onSuccess) {\n          onSuccess(newPasswordId);\n        }\n      } catch (error) {\n        var _error$data;\n        console.error(\"Error saving password:\", error);\n        setFormError((error === null || error === void 0 ? void 0 : (_error$data = error.data) === null || _error$data === void 0 ? void 0 : _error$data.message) || \"Failed to save password. Please try again.\");\n      }\n    } else {\n      setFormError(\"Please fill out all required fields before saving.\");\n    }\n    setIsLoading(false);\n  };\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  };\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n  const hasErrors = Object.keys(errors).length > 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-1.5 sm:p-2 bg-white/20 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                className: \"w-4 h-4 sm:w-6 sm:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg sm:text-xl font-semibold\",\n                  children: \"Add New Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white-100 text-xs sm:text-sm\",\n                  children: \"Secure your credentials with us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onCancel,\n            className: \"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\",\n        children: [formError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\",\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            className: \"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-left font-medium text-red-800 text-sm sm:text-base\",\n              children: \"Action Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-700 text-xs sm:text-sm\",\n              children: formError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${hasErrors ? \"min-h-[300px] sm:h-[340px]\" : \"min-h-[250px] sm:h-[280px]\"}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Building, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                  children: \"Organization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 sm:space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 34\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"department_id\",\n                    value: formData.department_id,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"department_id\"),\n                    onBlur: () => setFocusedField(null),\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none focus:ring-0 text-sm sm:text-base ${errors.department_id ? \"border-red-300 focus:border-red-500\" : focusedField === \"department_id\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Department\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 23\n                    }, this), isDepartmentsLoading && /*#__PURE__*/_jsxDEV(\"option\", {\n                      disabled: true,\n                      children: \"Loading...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 48\n                    }, this), departmentsError && /*#__PURE__*/_jsxDEV(\"option\", {\n                      disabled: true,\n                      children: \"Error loading departments\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 44\n                    }, this), departmentsData === null || departmentsData === void 0 ? void 0 : departmentsData.map(dept => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: dept.id,\n                      children: dept.name\n                    }, dept.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 21\n                  }, this), errors.department_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this), errors.department_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Team \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 28\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"team_id\",\n                    value: formData.team_id,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"team_id\"),\n                    onBlur: () => setFocusedField(null),\n                    disabled: !formData.department_id,\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${!formData.department_id ? \"bg-gray-100 border-gray-200 cursor-not-allowed\" : errors.team_id ? \"border-red-300 focus:border-red-500\" : focusedField === \"team_id\" ? \"border-primary-500 shadow-lg shadow-primary-100\" : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Team\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 23\n                    }, this), isTeamsLoading && /*#__PURE__*/_jsxDEV(\"option\", {\n                      disabled: true,\n                      children: \"Loading...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 42\n                    }, this), teamsError && /*#__PURE__*/_jsxDEV(\"option\", {\n                      disabled: true,\n                      children: \"Error loading teams\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 38\n                    }, this), formData.department_id && (teamsData === null || teamsData === void 0 ? void 0 : teamsData.filter(team => {\n                      var _team$department_ids;\n                      return (_team$department_ids = team.department_ids) === null || _team$department_ids === void 0 ? void 0 : _team$department_ids.includes(parseInt(formData.department_id));\n                    }).map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: team.id,\n                      children: team.name\n                    }, team.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 29\n                    }, this)))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), errors.team_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 25\n                    }, this), errors.team_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 23\n                  }, this), !formData.department_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1.5 sm:mt-2 text-xs sm:text-sm text-gray-500 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Users, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 25\n                    }, this), \"Please select a department first\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${hasErrors ? \"min-h-[300px] sm:h-[340px]\" : \"min-h-[250px] sm:h-[280px]\"}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                  children: \"Account Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col h-full space-y-3 sm:space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Platform Title \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 38\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"password_title\",\n                    value: formData.password_title,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"password_title\"),\n                    onBlur: () => setFocusedField(null),\n                    placeholder: \"e.g., Gmail, GitHub, AWS Console\",\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${errors.password_title ? \"border-red-300 focus:border-red-500\" : focusedField === \"password_title\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this), errors.password_title && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 25\n                    }, this), errors.password_title]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                    children: [\"Username \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"username\",\n                    value: formData.username,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"username\"),\n                    onBlur: () => setFocusedField(null),\n                    placeholder: \"Enter username or email\",\n                    className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${errors.username ? \"border-red-300 focus:border-red-500\" : focusedField === \"username\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 21\n                  }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 25\n                    }, this), errors.username]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 sm:mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-3 sm:mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Lock, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-gray-800 text-sm sm:text-base\",\n                children: \"Security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",\n                children: [\"Password \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 28\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? \"text\" : \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  onFocus: () => setFocusedField(\"password\"),\n                  onBlur: () => setFocusedField(null),\n                  placeholder: \"Enter password\",\n                  className: `w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-20 sm:pr-32 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${errors.password ? \"border-red-300 focus:border-red-500\" : focusedField === \"password\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 flex items-center space-x-0.5 sm:space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: useGeneratedPassword,\n                    disabled: !generatedPassword,\n                    className: `px-2 sm:px-3 py-1 sm:py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-0.5 sm:space-x-1 ${generatedPassword ? \"bg-primary/10-100 text-white-700 hover:bg-primary-200\" : \"bg-gray-100 text-gray-400 cursor-not-allowed\"}`,\n                    title: \"Use generated password\",\n                    children: [/*#__PURE__*/_jsxDEV(Wand2, {\n                      className: \"w-2.5 h-2.5 sm:w-3 sm:h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"hidden sm:inline\",\n                      children: \"Use\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: togglePasswordVisibility,\n                    className: \"p-1 sm:p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200\",\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this), errors.password]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 sm:mt-4 space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs sm:text-sm font-medium text-gray-700\",\n                    children: \"Password Strength\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-xs sm:text-sm font-medium px-2 py-1 rounded-full ${getStrengthColor(formData.strength)}`,\n                    children: formData.strength\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-200 rounded-full h-1.5 sm:h-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `h-1.5 sm:h-2 rounded-full transition-all duration-300 ${getStrengthProgress(formData.strength).color}`,\n                    style: {\n                      width: getStrengthProgress(formData.strength).width\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 text-xs sm:text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"All fields marked with * are required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onCancel,\n            className: \"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 font-medium text-sm sm:text-base\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleSubmit,\n            disabled: isLoading || isCreating || isUpdating,\n            className: \"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 bg-primary hover:bg-primary/90 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-500 focus:ring-offset-2 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 sm:space-x-2 text-sm sm:text-base\",\n            children: isLoading || isCreating || isUpdating ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: editData ? \"Updating...\" : \"Saving...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: editData ? \"Update\" : \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Shield, {\n                className: \"w-3 h-3 sm:w-4 sm:h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: editData ? \"Update Password\" : \"Save Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: editData ? \"Update\" : \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s(AddPasswordCardForm, \"IOTE50PRyvvWfzdADmxHh6043ng=\", false, function () {\n  return [useGetDepartmentsWithTeamsQuery, useGetTeamsWithDepartmentsQuery, useCreatePasswordManagerMutation, useUpdatePasswordManagerMutation];\n});\n_c = AddPasswordCardForm;\nexport default AddPasswordCardForm;\nvar _c;\n$RefreshReg$(_c, \"AddPasswordCardForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Eye", "Eye<PERSON>ff", "X", "Shield", "User", "Lock", "Building", "Users", "AlertCircle", "CheckCircle", "Wand2", "useCreatePasswordManagerMutation", "useUpdatePasswordManagerMutation", "useGetDepartmentsWithTeamsQuery", "useGetTeamsWithDepartmentsQuery", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddPasswordCardForm", "onCancel", "generatedPassword", "passwordStrength", "editData", "onSuccess", "departmentsData", "propDepartmentsData", "teamsData", "propTeamsData", "_s", "data", "departmentsDataApi", "isLoading", "isDepartmentsLoading", "error", "departmentsError", "undefined", "skip", "teamsDataApi", "isTeamsLoading", "teamsError", "createPasswordManager", "isCreating", "updatePasswordManager", "isUpdating", "formData", "setFormData", "password_title", "username", "password", "department_id", "team_id", "title", "String", "setErrors", "setFormError", "setShowPassword", "showPassword", "errors", "formError", "setIsLoading", "focusedField", "setFocusedField", "prev", "strength", "handleInputChange", "e", "name", "value", "target", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "getStrengthProgress", "width", "color", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "_result", "_result$data", "submitData", "parseInt", "newPasswordId", "result", "id", "unwrap", "_error$data", "console", "message", "useGeneratedPassword", "togglePasswordVisibility", "hasErrors", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "onFocus", "onBlur", "disabled", "map", "dept", "filter", "team", "_team$department_ids", "department_ids", "includes", "type", "placeholder", "style", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordCardForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport {\n  Eye,\n  EyeOff,\n  X,\n  Shield,\n  User,\n  Lock,\n  Building,\n  Users,\n  AlertCircle,\n  CheckCircle,\n  Wand2,\n} from \"lucide-react\";\nimport {\n  useCreatePasswordManagerMutation,\n  useUpdatePasswordManagerMutation,\n} from \"../../features/api/passwordManagerApi\";\nimport { useGetDepartmentsWithTeamsQuery } from \"../../features/api/departmentApi\";\nimport { useGetTeamsWithDepartmentsQuery } from \"../../features/api/teamApi\";\n\n// Main component for the Add Password Card form\nconst AddPasswordCardForm = ({\n  onCancel,\n  generatedPassword,\n  passwordStrength,\n  editData = null, // For editing existing password\n  onSuccess, // Callback after successful save\n  departmentsData: propDepartmentsData = null,\n  teamsData: propTeamsData = null,\n}) => {\n  // Use props if provided, otherwise fallback to API hooks\n  const { data: departmentsDataApi, isLoading: isDepartmentsLoading, error: departmentsError } = useGetDepartmentsWithTeamsQuery(undefined, { skip: !!propDepartmentsData });\n  const { data: teamsDataApi, isLoading: isTeamsLoading, error: teamsError } = useGetTeamsWithDepartmentsQuery(undefined, { skip: !!propTeamsData });\n  const departmentsData = propDepartmentsData || departmentsDataApi;\n  const teamsData = propTeamsData || teamsDataApi;\n  const [createPasswordManager, { isLoading: isCreating }] =\n    useCreatePasswordManagerMutation();\n  const [updatePasswordManager, { isLoading: isUpdating }] =\n    useUpdatePasswordManagerMutation();\n  // console.clear();\n\n  // console.log(departmentsData);\n\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    password_title: \"\",\n    username: \"\",\n    password: \"\",\n    department_id: \"\",\n    team_id: \"\",\n  });\n\n  // Initialize form data for editing or reset for new entries\n  useEffect(() => {\n    if (editData) {\n      setFormData({\n        password_title: editData.password_title || editData.title || \"\",\n        username: editData.username || \"\",\n        password: \"\", // Don't pre-fill password for security\n        department_id: editData.department_id\n          ? String(editData.department_id)\n          : \"\",\n        team_id: editData.team_id ? String(editData.team_id) : \"\",\n      });\n    } else {\n      // FIXED: Reset form completely when not editing (new entry)\n      setFormData({\n        password_title: \"\",\n        username: \"\",\n        password: \"\",\n        department_id: \"\",\n        team_id: \"\",\n      });\n      setErrors({});\n      setFormError(\"\");\n      setShowPassword(false);\n    }\n  }, [editData]);\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [focusedField, setFocusedField] = useState(null);\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData((prev) => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength,\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === \"department_id\") {\n      setFormData((prev) => ({ ...prev, [name]: value, team_id: \"\" }));\n    } else {\n      setFormData((prev) => ({ ...prev, [name]: value }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors((prev) => ({ ...prev, [name]: \"\" }));\n    }\n\n    // Clear form error when user starts typing\n    if (formError) {\n      setFormError(\"\");\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = (password) => {\n    if (!password) return \"Weak Password\";\n    let score = 0;\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return \"Strong Password\";\n    if (score >= 4) return \"Moderate Password\";\n    return \"Weak Password\";\n  };\n\n\n  \n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case \"Strong Password\":\n        return \"bg-emerald-50 text-emerald-700 border-emerald-200\";\n      case \"Moderate Password\":\n        return \"bg-amber-50 text-amber-700 border-amber-200\";\n      case \"Weak Password\":\n        return \"bg-red-50 text-red-700 border-red-200\";\n      default:\n        return \"bg-gray-50 text-gray-600 border-gray-200\";\n    }\n  };\n\n  // Get strength progress and color\n  const getStrengthProgress = (strength) => {\n    switch (strength) {\n      case \"Strong Password\":\n        return { width: \"100%\", color: \"bg-emerald-500\" };\n      case \"Moderate Password\":\n        return { width: \"66%\", color: \"bg-amber-500\" };\n      case \"Weak Password\":\n        return { width: \"33%\", color: \"bg-red-500\" };\n      default:\n        return { width: \"0%\", color: \"bg-gray-300\" };\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.password_title.trim())\n      newErrors.password_title = \"Platform title is required\";\n    if (!formData.username.trim()) newErrors.username = \"Username is required\";\n    if (!formData.password.trim()) newErrors.password = \"Password is required\";\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setFormError(\"\");\n    setIsLoading(true);\n\n    if (validateForm()) {\n      try {\n        const submitData = {\n          password_title: formData.password_title,\n          username: formData.username,\n          password: formData.password,\n          department_id: formData.department_id\n            ? parseInt(formData.department_id)\n            : null,\n          team_id: formData.team_id ? parseInt(formData.team_id) : null,\n        };\n\n        let newPasswordId = null;\n\n        let result;\n        if (editData) {\n          // Update existing password\n          result = await updatePasswordManager({\n            id: editData.id,\n            ...submitData,\n          }).unwrap();\n        } else {\n          // Create new password\n          result = await createPasswordManager(submitData).unwrap();\n        }\n\n        // Pass the new or updated item's ID to the success handler\n        newPasswordId = result?.data?.id;\n\n        // FIXED: Reset form after successful submission\n        setFormData({\n          password_title: \"\",\n          username: \"\",\n          password: \"\",\n          department_id: \"\",\n          team_id: \"\",\n        });\n        setErrors({});\n        setFormError(\"\");\n        setShowPassword(false);\n\n        // FIXED: Call success callback to refresh data and close modal\n        // The success callback will handle the toast notification\n        if (onSuccess) {\n          onSuccess(newPasswordId);\n        }\n      } catch (error) {\n        console.error(\"Error saving password:\", error);\n        setFormError(\n          error?.data?.message || \"Failed to save password. Please try again.\"\n        );\n      }\n    } else {\n      setFormError(\"Please fill out all required fields before saving.\");\n    }\n\n    setIsLoading(false);\n  };\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData((prev) => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength,\n      }));\n    }\n  };\n\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n\n  const hasErrors = Object.keys(errors).length > 0;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\">\n      <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\">\n        {/* Header - Responsive */}\n        <div className=\"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2 sm:space-x-3\">\n              <div className=\"p-1.5 sm:p-2 bg-white/20 rounded-lg\">\n                <Shield className=\"w-4 h-4 sm:w-6 sm:h-6\" />\n              </div>\n              <div>\n                <div className=\"text-left\">\n                  <h2 className=\"text-lg sm:text-xl font-semibold\">\n                    Add New Password\n                  </h2>\n                  <p className=\"text-white-100 text-xs sm:text-sm\">\n                    Secure your credentials with us\n                  </p>\n                </div>\n              </div>\n            </div>\n            <button\n              onClick={onCancel}\n              className=\"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\"\n            >\n              <X className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Form Content - Responsive */}\n        <div className=\"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\">\n          {/* Error Message - Responsive */}\n          {formError && (\n            <div className=\"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\">\n              <AlertCircle className=\"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\" />\n              <div>\n                <p className=\"text-left font-medium text-red-800 text-sm sm:text-base\">\n                  Action Required\n                </p>\n                <p className=\"text-red-700 text-xs sm:text-sm\">{formError}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Responsive Grid Layout */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\n            {/* Department & Team Section - Responsive */}\n            <div className=\"space-y-3 sm:space-y-4\">\n              <div\n                className={`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${\n                  hasErrors\n                    ? \"min-h-[300px] sm:h-[340px]\"\n                    : \"min-h-[250px] sm:h-[280px]\"\n                }`}\n              >\n                <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\n                  <Building className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\n                  <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\n                    Organization\n                  </h3>\n                </div>\n\n                <div className=\"space-y-3 sm:space-y-4\">\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Department <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      name=\"department_id\"\n                      value={formData.department_id}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"department_id\")}\n                      onBlur={() => setFocusedField(null)}\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none focus:ring-0 text-sm sm:text-base ${\n                        errors.department_id\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"department_id\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    >\n                      <option value=\"\">Select Department</option>\n                      {isDepartmentsLoading && <option disabled>Loading...</option>}\n                      {departmentsError && <option disabled>Error loading departments</option>}\n                      {departmentsData?.map((dept) => (\n                        <option key={dept.id} value={dept.id}>\n                          {dept.name}\n                        </option>\n                      ))}\n                    </select>\n                    {errors.department_id && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.department_id}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Team <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      name=\"team_id\"\n                      value={formData.team_id}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"team_id\")}\n                      onBlur={() => setFocusedField(null)}\n                      disabled={!formData.department_id}\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                        !formData.department_id\n                          ? \"bg-gray-100 border-gray-200 cursor-not-allowed\"\n                          : errors.team_id\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"team_id\"\n                          ? \"border-primary-500 shadow-lg shadow-primary-100\"\n                          : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"\n                      }`}\n                    >\n                      <option value=\"\">Select Team</option>\n                      {isTeamsLoading && <option disabled>Loading...</option>}\n                      {teamsError && <option disabled>Error loading teams</option>}\n                      {formData.department_id &&\n                        teamsData\n                          ?.filter((team) =>\n                            team.department_ids?.includes(\n                              parseInt(formData.department_id)\n                            )\n                          )\n                          .map((team) => (\n                            <option key={team.id} value={team.id}>\n                              {team.name}\n                            </option>\n                          ))}\n                    </select>\n                    {errors.team_id && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.team_id}\n                      </p>\n                    )}\n                    {!formData.department_id && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-gray-500 flex items-center\">\n                        <Users className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        Please select a department first\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Platform & Username Section - Responsive */}\n            <div className=\"space-y-3 sm:space-y-4\">\n              <div\n                className={`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${\n                  hasErrors\n                    ? \"min-h-[300px] sm:h-[340px]\"\n                    : \"min-h-[250px] sm:h-[280px]\"\n                }`}\n              >\n                <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\n                  <User className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\n                  <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\n                    Account Details\n                  </h3>\n                </div>\n\n                <div className=\"flex flex-col h-full space-y-3 sm:space-y-4\">\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Platform Title <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"password_title\"\n                      value={formData.password_title}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"password_title\")}\n                      onBlur={() => setFocusedField(null)}\n                      placeholder=\"e.g., Gmail, GitHub, AWS Console\"\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                        errors.password_title\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"password_title\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    />\n                    {errors.password_title && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.password_title}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Username <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"username\"\n                      value={formData.username}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"username\")}\n                      onBlur={() => setFocusedField(null)}\n                      placeholder=\"Enter username or email\"\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                        errors.username\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"username\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    />\n                    {errors.username && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.username}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Password Section - Responsive */}\n          <div className=\"mt-4 sm:mt-6\">\n            <div className=\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\">\n              <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\n                <Lock className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\n                <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\n                  Security\n                </h3>\n              </div>\n\n              <div>\n                <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                  Password <span className=\"text-red-500\">*</span>\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showPassword ? \"text\" : \"password\"}\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    onFocus={() => setFocusedField(\"password\")}\n                    onBlur={() => setFocusedField(null)}\n                    placeholder=\"Enter password\"\n                    className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-20 sm:pr-32 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                      errors.password\n                        ? \"border-red-300 focus:border-red-500\"\n                        : focusedField === \"password\"\n                        ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                        : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"\n                    }`}\n                  />\n\n                  {/* Responsive Button Container */}\n                  <div className=\"absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 flex items-center space-x-0.5 sm:space-x-1\">\n                    <button\n                      type=\"button\"\n                      onClick={useGeneratedPassword}\n                      disabled={!generatedPassword}\n                      className={`px-2 sm:px-3 py-1 sm:py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-0.5 sm:space-x-1 ${\n                        generatedPassword\n                          ? \"bg-primary/10-100 text-white-700 hover:bg-primary-200\"\n                          : \"bg-gray-100 text-gray-400 cursor-not-allowed\"\n                      }`}\n                      title=\"Use generated password\"\n                    >\n                      <Wand2 className=\"w-2.5 h-2.5 sm:w-3 sm:h-3\" />\n                      <span className=\"hidden sm:inline\">Use</span>\n                    </button>\n\n                    <button\n                      type=\"button\"\n                      onClick={togglePasswordVisibility}\n                      className=\"p-1 sm:p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200\"\n                    >\n                      {showPassword ? (\n                        <EyeOff className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                      ) : (\n                        <Eye className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                      )}\n                    </button>\n                  </div>\n                </div>\n\n                {errors.password && (\n                  <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                    <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                    {errors.password}\n                  </p>\n                )}\n\n                {/* Password Strength Indicator - Responsive */}\n                {formData.password && (\n                  <div className=\"mt-3 sm:mt-4 space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-xs sm:text-sm font-medium text-gray-700\">\n                        Password Strength\n                      </span>\n                      <span\n                        className={`text-xs sm:text-sm font-medium px-2 py-1 rounded-full ${getStrengthColor(\n                          formData.strength\n                        )}`}\n                      >\n                        {formData.strength}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-1.5 sm:h-2\">\n                      <div\n                        className={`h-1.5 sm:h-2 rounded-full transition-all duration-300 ${\n                          getStrengthProgress(formData.strength).color\n                        }`}\n                        style={{\n                          width: getStrengthProgress(formData.strength).width,\n                        }}\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer - Responsive */}\n        <div className=\"px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0\">\n          <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-gray-500\">\n            <CheckCircle className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n            <span>All fields marked with * are required</span>\n          </div>\n\n          {/* Responsive Button Container */}\n          <div className=\"flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto\">\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 font-medium text-sm sm:text-base\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={handleSubmit}\n              disabled={isLoading || isCreating || isUpdating}\n              className=\"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 bg-primary hover:bg-primary/90 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-500 focus:ring-offset-2 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 sm:space-x-2 text-sm sm:text-base\"\n            >\n              {isLoading || isCreating || isUpdating ? (\n                <>\n                  <div className=\"w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span className=\"hidden sm:inline\">\n                    {editData ? \"Updating...\" : \"Saving...\"}\n                  </span>\n                  <span className=\"sm:hidden\">\n                    {editData ? \"Update\" : \"Save\"}\n                  </span>\n                </>\n              ) : (\n                <>\n                  <Shield className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                  <span className=\"hidden sm:inline\">\n                    {editData ? \"Update Password\" : \"Save Password\"}\n                  </span>\n                  <span className=\"sm:hidden\">\n                    {editData ? \"Update\" : \"Save\"}\n                  </span>\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddPasswordCardForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,MAAM,EACNC,CAAC,EACDC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,WAAW,EACXC,KAAK,QACA,cAAc;AACrB,SACEC,gCAAgC,EAChCC,gCAAgC,QAC3B,uCAAuC;AAC9C,SAASC,+BAA+B,QAAQ,kCAAkC;AAClF,SAASC,+BAA+B,QAAQ,4BAA4B;;AAE5E;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,QAAQ;EACRC,iBAAiB;EACjBC,gBAAgB;EAChBC,QAAQ,GAAG,IAAI;EAAE;EACjBC,SAAS;EAAE;EACXC,eAAe,EAAEC,mBAAmB,GAAG,IAAI;EAC3CC,SAAS,EAAEC,aAAa,GAAG;AAC7B,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM;IAAEC,IAAI,EAAEC,kBAAkB;IAAEC,SAAS,EAAEC,oBAAoB;IAAEC,KAAK,EAAEC;EAAiB,CAAC,GAAGtB,+BAA+B,CAACuB,SAAS,EAAE;IAAEC,IAAI,EAAE,CAAC,CAACX;EAAoB,CAAC,CAAC;EAC1K,MAAM;IAAEI,IAAI,EAAEQ,YAAY;IAAEN,SAAS,EAAEO,cAAc;IAAEL,KAAK,EAAEM;EAAW,CAAC,GAAG1B,+BAA+B,CAACsB,SAAS,EAAE;IAAEC,IAAI,EAAE,CAAC,CAACT;EAAc,CAAC,CAAC;EAClJ,MAAMH,eAAe,GAAGC,mBAAmB,IAAIK,kBAAkB;EACjE,MAAMJ,SAAS,GAAGC,aAAa,IAAIU,YAAY;EAC/C,MAAM,CAACG,qBAAqB,EAAE;IAAET,SAAS,EAAEU;EAAW,CAAC,CAAC,GACtD/B,gCAAgC,CAAC,CAAC;EACpC,MAAM,CAACgC,qBAAqB,EAAE;IAAEX,SAAS,EAAEY;EAAW,CAAC,CAAC,GACtDhC,gCAAgC,CAAC,CAAC;EACpC;;EAEA;;EAEA;EACA,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC;IACvCiD,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACApD,SAAS,CAAC,MAAM;IACd,IAAIwB,QAAQ,EAAE;MACZuB,WAAW,CAAC;QACVC,cAAc,EAAExB,QAAQ,CAACwB,cAAc,IAAIxB,QAAQ,CAAC6B,KAAK,IAAI,EAAE;QAC/DJ,QAAQ,EAAEzB,QAAQ,CAACyB,QAAQ,IAAI,EAAE;QACjCC,QAAQ,EAAE,EAAE;QAAE;QACdC,aAAa,EAAE3B,QAAQ,CAAC2B,aAAa,GACjCG,MAAM,CAAC9B,QAAQ,CAAC2B,aAAa,CAAC,GAC9B,EAAE;QACNC,OAAO,EAAE5B,QAAQ,CAAC4B,OAAO,GAAGE,MAAM,CAAC9B,QAAQ,CAAC4B,OAAO,CAAC,GAAG;MACzD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAL,WAAW,CAAC;QACVC,cAAc,EAAE,EAAE;QAClBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,aAAa,EAAE,EAAE;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC;MACFG,SAAS,CAAC,CAAC,CAAC,CAAC;MACbC,YAAY,CAAC,EAAE,CAAC;MAChBC,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM,CAACkC,YAAY,EAAED,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,MAAM,EAAEJ,SAAS,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC6D,SAAS,EAAEJ,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,SAAS,EAAE4B,YAAY,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIsB,iBAAiB,EAAE;MACrByB,WAAW,CAAEiB,IAAI,KAAM;QACrB,GAAGA,IAAI;QACPd,QAAQ,EAAE5B,iBAAiB;QAC3B2C,QAAQ,EAAE1C;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACD,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;;EAEzC;EACA,MAAM2C,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,eAAe,EAAE;MAC5BrB,WAAW,CAAEiB,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAE,CAACI,IAAI,GAAGC,KAAK;QAAEjB,OAAO,EAAE;MAAG,CAAC,CAAC,CAAC;IAClE,CAAC,MAAM;MACLL,WAAW,CAAEiB,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAE,CAACI,IAAI,GAAGC;MAAM,CAAC,CAAC,CAAC;IACrD;;IAEA;IACA,IAAIV,MAAM,CAACS,IAAI,CAAC,EAAE;MAChBb,SAAS,CAAES,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAE,CAACI,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAChD;;IAEA;IACA,IAAIR,SAAS,EAAE;MACbJ,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMe,yBAAyB,GAAIrB,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,eAAe;IACrC,IAAIsB,KAAK,GAAG,CAAC;IACb,IAAItB,QAAQ,CAACuB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC,CAAC,KACjC,IAAItB,QAAQ,CAACuB,MAAM,IAAI,CAAC,EAAED,KAAK,IAAI,CAAC;IACzC,IAAI,OAAO,CAACE,IAAI,CAACxB,QAAQ,CAAC,EAAEsB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACxB,QAAQ,CAAC,EAAEsB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACxB,QAAQ,CAAC,EAAEsB,KAAK,IAAI,CAAC;IACtC,IAAI,cAAc,CAACE,IAAI,CAACxB,QAAQ,CAAC,EAAEsB,KAAK,IAAI,CAAC;IAC7C,IAAItB,QAAQ,CAACuB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC;IACrC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;;EAKD;EACA,MAAMG,gBAAgB,GAAIV,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO,mDAAmD;MAC5D,KAAK,mBAAmB;QACtB,OAAO,6CAA6C;MACtD,KAAK,eAAe;QAClB,OAAO,uCAAuC;MAChD;QACE,OAAO,0CAA0C;IACrD;EACF,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAIX,QAAQ,IAAK;IACxC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO;UAAEY,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAiB,CAAC;MACnD,KAAK,mBAAmB;QACtB,OAAO;UAAED,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAe,CAAC;MAChD,KAAK,eAAe;QAClB,OAAO;UAAED,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAa,CAAC;MAC9C;QACE,OAAO;UAAED,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAc,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAI,CAAClC,QAAQ,CAACE,cAAc,CAACiC,IAAI,CAAC,CAAC,EACjCD,SAAS,CAAChC,cAAc,GAAG,4BAA4B;IACzD,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAACgC,IAAI,CAAC,CAAC,EAAED,SAAS,CAAC/B,QAAQ,GAAG,sBAAsB;IAC1E,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAAC+B,IAAI,CAAC,CAAC,EAAED,SAAS,CAAC9B,QAAQ,GAAG,sBAAsB;IAC1EK,SAAS,CAACyB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACP,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMW,YAAY,GAAG,MAAOjB,CAAC,IAAK;IAChCA,CAAC,CAACkB,cAAc,CAAC,CAAC;IAClB7B,YAAY,CAAC,EAAE,CAAC;IAChBK,YAAY,CAAC,IAAI,CAAC;IAElB,IAAIkB,YAAY,CAAC,CAAC,EAAE;MAClB,IAAI;QAAA,IAAAO,OAAA,EAAAC,YAAA;QACF,MAAMC,UAAU,GAAG;UACjBxC,cAAc,EAAEF,QAAQ,CAACE,cAAc;UACvCC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;UAC3BC,aAAa,EAAEL,QAAQ,CAACK,aAAa,GACjCsC,QAAQ,CAAC3C,QAAQ,CAACK,aAAa,CAAC,GAChC,IAAI;UACRC,OAAO,EAAEN,QAAQ,CAACM,OAAO,GAAGqC,QAAQ,CAAC3C,QAAQ,CAACM,OAAO,CAAC,GAAG;QAC3D,CAAC;QAED,IAAIsC,aAAa,GAAG,IAAI;QAExB,IAAIC,MAAM;QACV,IAAInE,QAAQ,EAAE;UACZ;UACAmE,MAAM,GAAG,MAAM/C,qBAAqB,CAAC;YACnCgD,EAAE,EAAEpE,QAAQ,CAACoE,EAAE;YACf,GAAGJ;UACL,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC;QACb,CAAC,MAAM;UACL;UACAF,MAAM,GAAG,MAAMjD,qBAAqB,CAAC8C,UAAU,CAAC,CAACK,MAAM,CAAC,CAAC;QAC3D;;QAEA;QACAH,aAAa,IAAAJ,OAAA,GAAGK,MAAM,cAAAL,OAAA,wBAAAC,YAAA,GAAND,OAAA,CAAQvD,IAAI,cAAAwD,YAAA,uBAAZA,YAAA,CAAcK,EAAE;;QAEhC;QACA7C,WAAW,CAAC;UACVC,cAAc,EAAE,EAAE;UAClBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,EAAE;UACjBC,OAAO,EAAE;QACX,CAAC,CAAC;QACFG,SAAS,CAAC,CAAC,CAAC,CAAC;QACbC,YAAY,CAAC,EAAE,CAAC;QAChBC,eAAe,CAAC,KAAK,CAAC;;QAEtB;QACA;QACA,IAAIhC,SAAS,EAAE;UACbA,SAAS,CAACiE,aAAa,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOvD,KAAK,EAAE;QAAA,IAAA2D,WAAA;QACdC,OAAO,CAAC5D,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CqB,YAAY,CACV,CAAArB,KAAK,aAALA,KAAK,wBAAA2D,WAAA,GAAL3D,KAAK,CAAEJ,IAAI,cAAA+D,WAAA,uBAAXA,WAAA,CAAaE,OAAO,KAAI,4CAC1B,CAAC;MACH;IACF,CAAC,MAAM;MACLxC,YAAY,CAAC,oDAAoD,CAAC;IACpE;IAEAK,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMoC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI3E,iBAAiB,EAAE;MACrByB,WAAW,CAAEiB,IAAI,KAAM;QACrB,GAAGA,IAAI;QACPd,QAAQ,EAAE5B,iBAAiB;QAC3B2C,QAAQ,EAAE1C;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAM2E,wBAAwB,GAAGA,CAAA,KAAMzC,eAAe,CAAC,CAACC,YAAY,CAAC;EAErE,MAAMyC,SAAS,GAAGjB,MAAM,CAACC,IAAI,CAACxB,MAAM,CAAC,CAACc,MAAM,GAAG,CAAC;EAEhD,oBACExD,OAAA;IAAKmF,SAAS,EAAC,6HAA6H;IAAAC,QAAA,eAC1IpF,OAAA;MAAKmF,SAAS,EAAC,iMAAiM;MAAAC,QAAA,gBAE9MpF,OAAA;QAAKmF,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DpF,OAAA;UAAKmF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDpF,OAAA;YAAKmF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDpF,OAAA;cAAKmF,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClDpF,OAAA,CAACb,MAAM;gBAACgG,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNxF,OAAA;cAAAoF,QAAA,eACEpF,OAAA;gBAAKmF,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpF,OAAA;kBAAImF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxF,OAAA;kBAAGmF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxF,OAAA;YACEyF,OAAO,EAAErF,QAAS;YAClB+E,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eAEpFpF,OAAA,CAACd,CAAC;cAACiG,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxF,OAAA;QAAKmF,SAAS,EAAC,iFAAiF;QAAAC,QAAA,GAE7FzC,SAAS,iBACR3C,OAAA;UAAKmF,SAAS,EAAC,yJAAyJ;UAAAC,QAAA,gBACtKpF,OAAA,CAACR,WAAW;YAAC2F,SAAS,EAAC;UAAyD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAGmF,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJxF,OAAA;cAAGmF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEzC;YAAS;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDxF,OAAA;UAAKmF,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAE7DpF,OAAA;YAAKmF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACrCpF,OAAA;cACEmF,SAAS,EAAE,yEACTD,SAAS,GACL,4BAA4B,GAC5B,4BAA4B,EAC/B;cAAAE,QAAA,gBAEHpF,OAAA;gBAAKmF,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvDpF,OAAA,CAACV,QAAQ;kBAAC6F,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DxF,OAAA;kBAAImF,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE/D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENxF,OAAA;gBAAKmF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAOmF,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,aAClF,eAAApF,OAAA;sBAAMmF,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACRxF,OAAA;oBACEmD,IAAI,EAAC,eAAe;oBACpBC,KAAK,EAAEvB,QAAQ,CAACK,aAAc;oBAC9BwD,QAAQ,EAAEzC,iBAAkB;oBAC5B0C,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,eAAe,CAAE;oBAChD8C,MAAM,EAAEA,CAAA,KAAM9C,eAAe,CAAC,IAAI,CAAE;oBACpCqC,SAAS,EAAE,2IACTzC,MAAM,CAACR,aAAa,GAChB,qCAAqC,GACrCW,YAAY,KAAK,eAAe,GAChC,oDAAoD,GACpD,4DAA4D,EAC/D;oBAAAuC,QAAA,gBAEHpF,OAAA;sBAAQoD,KAAK,EAAC,EAAE;sBAAAgC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1CvE,oBAAoB,iBAAIjB,OAAA;sBAAQ6F,QAAQ;sBAAAT,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC5DrE,gBAAgB,iBAAInB,OAAA;sBAAQ6F,QAAQ;sBAAAT,QAAA,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACvE/E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqF,GAAG,CAAEC,IAAI,iBACzB/F,OAAA;sBAAsBoD,KAAK,EAAE2C,IAAI,CAACpB,EAAG;sBAAAS,QAAA,EAClCW,IAAI,CAAC5C;oBAAI,GADC4C,IAAI,CAACpB,EAAE;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEZ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,EACR9C,MAAM,CAACR,aAAa,iBACnBlC,OAAA;oBAAGmF,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC7EpF,OAAA,CAACR,WAAW;sBAAC2F,SAAS,EAAC;oBAA4B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACrD9C,MAAM,CAACR,aAAa;kBAAA;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENxF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAOmF,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,OACxF,eAAApF,OAAA;sBAAMmF,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACRxF,OAAA;oBACEmD,IAAI,EAAC,SAAS;oBACdC,KAAK,EAAEvB,QAAQ,CAACM,OAAQ;oBACxBuD,QAAQ,EAAEzC,iBAAkB;oBAC5B0C,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,SAAS,CAAE;oBAC1C8C,MAAM,EAAEA,CAAA,KAAM9C,eAAe,CAAC,IAAI,CAAE;oBACpC+C,QAAQ,EAAE,CAAChE,QAAQ,CAACK,aAAc;oBAClCiD,SAAS,EAAE,8HACT,CAACtD,QAAQ,CAACK,aAAa,GACnB,gDAAgD,GAChDQ,MAAM,CAACP,OAAO,GACd,qCAAqC,GACrCU,YAAY,KAAK,SAAS,GAC1B,iDAAiD,GACjD,gEAAgE,EACnE;oBAAAuC,QAAA,gBAEHpF,OAAA;sBAAQoD,KAAK,EAAC,EAAE;sBAAAgC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACpCjE,cAAc,iBAAIvB,OAAA;sBAAQ6F,QAAQ;sBAAAT,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACtDhE,UAAU,iBAAIxB,OAAA;sBAAQ6F,QAAQ;sBAAAT,QAAA,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC3D3D,QAAQ,CAACK,aAAa,KACrBvB,SAAS,aAATA,SAAS,uBAATA,SAAS,CACLqF,MAAM,CAAEC,IAAI;sBAAA,IAAAC,oBAAA;sBAAA,QAAAA,oBAAA,GACZD,IAAI,CAACE,cAAc,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,QAAQ,CAC3B5B,QAAQ,CAAC3C,QAAQ,CAACK,aAAa,CACjC,CAAC;oBAAA,CACH,CAAC,CACA4D,GAAG,CAAEG,IAAI,iBACRjG,OAAA;sBAAsBoD,KAAK,EAAE6C,IAAI,CAACtB,EAAG;sBAAAS,QAAA,EAClCa,IAAI,CAAC9C;oBAAI,GADC8C,IAAI,CAACtB,EAAE;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEZ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,EACR9C,MAAM,CAACP,OAAO,iBACbnC,OAAA;oBAAGmF,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC7EpF,OAAA,CAACR,WAAW;sBAAC2F,SAAS,EAAC;oBAA4B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACrD9C,MAAM,CAACP,OAAO;kBAAA;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CACJ,EACA,CAAC3D,QAAQ,CAACK,aAAa,iBACtBlC,OAAA;oBAAGmF,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,gBAC9EpF,OAAA,CAACT,KAAK;sBAAC4F,SAAS,EAAC;oBAA4B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oCAElD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxF,OAAA;YAAKmF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACrCpF,OAAA;cACEmF,SAAS,EAAE,yEACTD,SAAS,GACL,4BAA4B,GAC5B,4BAA4B,EAC/B;cAAAE,QAAA,gBAEHpF,OAAA;gBAAKmF,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvDpF,OAAA,CAACZ,IAAI;kBAAC+F,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDxF,OAAA;kBAAImF,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE/D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENxF,OAAA;gBAAKmF,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAOmF,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,iBAC9E,eAAApF,OAAA;sBAAMmF,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACRxF,OAAA;oBACEqG,IAAI,EAAC,MAAM;oBACXlD,IAAI,EAAC,gBAAgB;oBACrBC,KAAK,EAAEvB,QAAQ,CAACE,cAAe;oBAC/B2D,QAAQ,EAAEzC,iBAAkB;oBAC5B0C,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,gBAAgB,CAAE;oBACjD8C,MAAM,EAAEA,CAAA,KAAM9C,eAAe,CAAC,IAAI,CAAE;oBACpCwD,WAAW,EAAC,kCAAkC;oBAC9CnB,SAAS,EAAE,8HACTzC,MAAM,CAACX,cAAc,GACjB,qCAAqC,GACrCc,YAAY,KAAK,gBAAgB,GACjC,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACD9C,MAAM,CAACX,cAAc,iBACpB/B,OAAA;oBAAGmF,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC7EpF,OAAA,CAACR,WAAW;sBAAC2F,SAAS,EAAC;oBAA4B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACrD9C,MAAM,CAACX,cAAc;kBAAA;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENxF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAOmF,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,GAAC,WACpF,eAAApF,OAAA;sBAAMmF,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACRxF,OAAA;oBACEqG,IAAI,EAAC,MAAM;oBACXlD,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAEvB,QAAQ,CAACG,QAAS;oBACzB0D,QAAQ,EAAEzC,iBAAkB;oBAC5B0C,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,UAAU,CAAE;oBAC3C8C,MAAM,EAAEA,CAAA,KAAM9C,eAAe,CAAC,IAAI,CAAE;oBACpCwD,WAAW,EAAC,yBAAyB;oBACrCnB,SAAS,EAAE,8HACTzC,MAAM,CAACV,QAAQ,GACX,qCAAqC,GACrCa,YAAY,KAAK,UAAU,GAC3B,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACD9C,MAAM,CAACV,QAAQ,iBACdhC,OAAA;oBAAGmF,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC7EpF,OAAA,CAACR,WAAW;sBAAC2F,SAAS,EAAC;oBAA4B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACrD9C,MAAM,CAACV,QAAQ;kBAAA;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxF,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BpF,OAAA;YAAKmF,SAAS,EAAC,uEAAuE;YAAAC,QAAA,gBACpFpF,OAAA;cAAKmF,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACvDpF,OAAA,CAACX,IAAI;gBAAC8F,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDxF,OAAA;gBAAImF,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAE/D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENxF,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAOmF,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,GAAC,WACpF,eAAApF,OAAA;kBAAMmF,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACRxF,OAAA;gBAAKmF,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpF,OAAA;kBACEqG,IAAI,EAAE5D,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCU,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvB,QAAQ,CAACI,QAAS;kBACzByD,QAAQ,EAAEzC,iBAAkB;kBAC5B0C,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,UAAU,CAAE;kBAC3C8C,MAAM,EAAEA,CAAA,KAAM9C,eAAe,CAAC,IAAI,CAAE;kBACpCwD,WAAW,EAAC,gBAAgB;kBAC5BnB,SAAS,EAAE,6IACTzC,MAAM,CAACT,QAAQ,GACX,qCAAqC,GACrCY,YAAY,KAAK,UAAU,GAC3B,oDAAoD,GACpD,gEAAgE;gBACnE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGFxF,OAAA;kBAAKmF,SAAS,EAAC,iGAAiG;kBAAAC,QAAA,gBAC9GpF,OAAA;oBACEqG,IAAI,EAAC,QAAQ;oBACbZ,OAAO,EAAET,oBAAqB;oBAC9Ba,QAAQ,EAAE,CAACxF,iBAAkB;oBAC7B8E,SAAS,EAAE,qIACT9E,iBAAiB,GACb,uDAAuD,GACvD,8CAA8C,EACjD;oBACH+B,KAAK,EAAC,wBAAwB;oBAAAgD,QAAA,gBAE9BpF,OAAA,CAACN,KAAK;sBAACyF,SAAS,EAAC;oBAA2B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/CxF,OAAA;sBAAMmF,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eAETxF,OAAA;oBACEqG,IAAI,EAAC,QAAQ;oBACbZ,OAAO,EAAER,wBAAyB;oBAClCE,SAAS,EAAC,4GAA4G;oBAAAC,QAAA,EAErH3C,YAAY,gBACXzC,OAAA,CAACf,MAAM;sBAACkG,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE5CxF,OAAA,CAAChB,GAAG;sBAACmG,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACzC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL9C,MAAM,CAACT,QAAQ,iBACdjC,OAAA;gBAAGmF,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,gBAC7EpF,OAAA,CAACR,WAAW;kBAAC2F,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrD9C,MAAM,CAACT,QAAQ;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACJ,EAGA3D,QAAQ,CAACI,QAAQ,iBAChBjC,OAAA;gBAAKmF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpF,OAAA;kBAAKmF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDpF,OAAA;oBAAMmF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAE/D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPxF,OAAA;oBACEmF,SAAS,EAAE,yDAAyDzB,gBAAgB,CAClF7B,QAAQ,CAACmB,QACX,CAAC,EAAG;oBAAAoC,QAAA,EAEHvD,QAAQ,CAACmB;kBAAQ;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxF,OAAA;kBAAKmF,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,eAC3DpF,OAAA;oBACEmF,SAAS,EAAE,yDACTxB,mBAAmB,CAAC9B,QAAQ,CAACmB,QAAQ,CAAC,CAACa,KAAK,EAC3C;oBACH0C,KAAK,EAAE;sBACL3C,KAAK,EAAED,mBAAmB,CAAC9B,QAAQ,CAACmB,QAAQ,CAAC,CAACY;oBAChD;kBAAE;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxF,OAAA;QAAKmF,SAAS,EAAC,oJAAoJ;QAAAC,QAAA,gBACjKpF,OAAA;UAAKmF,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EpF,OAAA,CAACP,WAAW;YAAC0F,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDxF,OAAA;YAAAoF,QAAA,EAAM;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAGNxF,OAAA;UAAKmF,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxEpF,OAAA;YACEqG,IAAI,EAAC,QAAQ;YACbZ,OAAO,EAAErF,QAAS;YAClB+E,SAAS,EAAC,mQAAmQ;YAAAC,QAAA,EAC9Q;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA;YACEqG,IAAI,EAAC,QAAQ;YACbZ,OAAO,EAAEtB,YAAa;YACtB0B,QAAQ,EAAE7E,SAAS,IAAIU,UAAU,IAAIE,UAAW;YAChDuD,SAAS,EAAC,qVAAqV;YAAAC,QAAA,EAE9VpE,SAAS,IAAIU,UAAU,IAAIE,UAAU,gBACpC5B,OAAA,CAAAE,SAAA;cAAAkF,QAAA,gBACEpF,OAAA;gBAAKmF,SAAS,EAAC;cAA4F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClHxF,OAAA;gBAAMmF,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC/B7E,QAAQ,GAAG,aAAa,GAAG;cAAW;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACPxF,OAAA;gBAAMmF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACxB7E,QAAQ,GAAG,QAAQ,GAAG;cAAM;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,eACP,CAAC,gBAEHxF,OAAA,CAAAE,SAAA;cAAAkF,QAAA,gBACEpF,OAAA,CAACb,MAAM;gBAACgG,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5CxF,OAAA;gBAAMmF,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC/B7E,QAAQ,GAAG,iBAAiB,GAAG;cAAe;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACPxF,OAAA;gBAAMmF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACxB7E,QAAQ,GAAG,QAAQ,GAAG;cAAM;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,eACP;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAzmBIV,mBAAmB;EAAA,QAUwEN,+BAA+B,EACjDC,+BAA+B,EAI1GH,gCAAgC,EAEhCC,gCAAgC;AAAA;AAAA4G,EAAA,GAjB9BrG,mBAAmB;AA2mBzB,eAAeA,mBAAmB;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}