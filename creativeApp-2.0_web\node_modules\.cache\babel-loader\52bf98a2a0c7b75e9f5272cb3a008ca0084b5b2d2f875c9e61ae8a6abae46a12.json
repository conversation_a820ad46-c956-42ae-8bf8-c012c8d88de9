{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\weatherAndTime\\\\WeatherData.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { fetchWeatherApi } from \"openmeteo\";\nimport CustomClock from \"./CustomClock\";\nimport weatherBackground from \"../../assets/images/\";\nimport weatherIcon from \"../../assets/images/icon-sun-cloud.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WeatherData = () => {\n  _s();\n  const [weatherData, setWeatherData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [ipData, setIpData] = useState(false);\n  const [data5Days, set5DaysData] = useState(false);\n  const background = {\n    backgroundImage: `url(${weatherBackground})`,\n    backgroundPosition: 'center',\n    backgroundSize: 'cover'\n  };\n  // Fetch IP data with multiple API fallbacks\n  useEffect(() => {\n    const fetchLocationData = async () => {\n      // For development, use a fixed location to avoid CORS issues\n      if (process.env.NODE_ENV === 'development') {\n        console.log('Development mode: Using fixed location');\n        setIpData({\n          lat: 23.8103,\n          lon: 90.4125,\n          city: 'Dhaka',\n          country: 'Bangladesh',\n          timezone: 'Asia/Dhaka'\n        });\n        return;\n      }\n\n      // For production, add a timeout to ensure we don't wait too long\n      const timeoutPromise = new Promise((_, reject) => {\n        setTimeout(() => reject(new Error('API timeout')), 10000); // 10 second timeout\n      });\n      const geoApis = [{\n        url: 'https://reallyfreegeoip.org/json/',\n        transform: data => ({\n          lat: data.latitude,\n          lon: data.longitude,\n          city: data.city,\n          country: data.country_name,\n          timezone: data.time_zone\n        })\n      }, {\n        url: 'https://freegeoip.tech/json/',\n        transform: data => ({\n          lat: data.latitude,\n          lon: data.longitude,\n          city: data.city,\n          country: data.country_name,\n          timezone: data.time_zone\n        })\n      }, {\n        url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free',\n        transform: data => {\n          var _data$time_zone;\n          return {\n            lat: parseFloat(data.latitude),\n            lon: parseFloat(data.longitude),\n            city: data.city,\n            country: data.country_name,\n            timezone: ((_data$time_zone = data.time_zone) === null || _data$time_zone === void 0 ? void 0 : _data$time_zone.name) || data.timezone\n          };\n        }\n      }, {\n        url: 'https://freeipapi.com/api/json',\n        transform: data => ({\n          lat: data.latitude,\n          lon: data.longitude,\n          city: data.cityName,\n          country: data.countryName,\n          timezone: data.timeZone\n        })\n      }];\n\n      // First try browser geolocation if available\n      if (navigator.geolocation) {\n        try {\n          const position = await new Promise((resolve, reject) => {\n            navigator.geolocation.getCurrentPosition(resolve, reject, {\n              timeout: 5000,\n              enableHighAccuracy: false\n            });\n          });\n          console.log('Using browser geolocation');\n          setIpData({\n            lat: position.coords.latitude,\n            lon: position.coords.longitude,\n            city: 'Current Location',\n            country: 'Current Location',\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone\n          });\n          return;\n        } catch (geoError) {\n          console.log('Browser geolocation failed, trying IP APIs:', geoError.message);\n        }\n      }\n\n      // Try each API until one works\n      for (let i = 0; i < geoApis.length; i++) {\n        const api = geoApis[i];\n        try {\n          console.log(`Trying API ${i + 1}/${geoApis.length}: ${api.url}`);\n          const controller = new AbortController();\n          const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout per API\n\n          const response = await fetch(api.url, {\n            method: 'GET',\n            headers: {\n              'Accept': 'application/json',\n              'User-Agent': 'Mozilla/5.0 (compatible; WeatherApp/1.0)'\n            },\n            mode: 'cors',\n            signal: controller.signal\n          });\n          clearTimeout(timeoutId);\n          if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n          }\n          const data = await response.json();\n          console.log(`API ${i + 1} Response:`, data);\n\n          // Transform data to consistent format\n          const transformedData = api.transform(data);\n\n          // Validate required fields\n          if (transformedData.lat && transformedData.lon && !isNaN(transformedData.lat) && !isNaN(transformedData.lon)) {\n            console.log(`✅ Successfully fetched location data from API ${i + 1}:`, transformedData);\n            setIpData(transformedData);\n            return; // Success, exit the loop\n          } else {\n            throw new Error('Invalid or incomplete location data received');\n          }\n        } catch (err) {\n          console.warn(`❌ API ${i + 1} failed (${api.url}):`, err.message);\n\n          // Special handling for the lead's recommended APIs\n          if (i < 2) {\n            console.warn(`⚠️  Lead's recommended API ${i + 1} failed. This might need attention.`);\n          }\n\n          // Continue to next API\n        }\n      }\n\n      // If all APIs fail, try a simple approach with user's timezone\n      console.error('All geolocation APIs failed, using timezone-based location');\n\n      // Get user's timezone and set approximate location\n      const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n      let defaultLocation = {\n        lat: 40.7128,\n        lon: -74.0060,\n        city: 'New York',\n        country: 'United States',\n        timezone: 'America/New_York'\n      };\n\n      // Set location based on common timezones\n      if (userTimezone.includes('Asia/Dhaka')) {\n        defaultLocation = {\n          lat: 23.8103,\n          lon: 90.4125,\n          city: 'Dhaka',\n          country: 'Bangladesh',\n          timezone: 'Asia/Dhaka'\n        };\n      } else if (userTimezone.includes('Europe/London')) {\n        defaultLocation = {\n          lat: 51.5074,\n          lon: -0.1278,\n          city: 'London',\n          country: 'United Kingdom',\n          timezone: 'Europe/London'\n        };\n      } else if (userTimezone.includes('America/Los_Angeles')) {\n        defaultLocation = {\n          lat: 34.0522,\n          lon: -118.2437,\n          city: 'Los Angeles',\n          country: 'United States',\n          timezone: 'America/Los_Angeles'\n        };\n      }\n      console.log('Using timezone-based location:', defaultLocation);\n      setIpData(defaultLocation);\n    };\n    fetchLocationData();\n  }, []);\n  // traffic url generate\n  const trafficUrl = `https://www.google.com/maps/@${ipData === null || ipData === void 0 ? void 0 : ipData.lat},${ipData === null || ipData === void 0 ? void 0 : ipData.lon}`;\n  const params = {\n    latitude: ipData === null || ipData === void 0 ? void 0 : ipData.lat,\n    longitude: ipData === null || ipData === void 0 ? void 0 : ipData.lon,\n    hourly: [\"temperature_2m\", \"relative_humidity_2m\", \"apparent_temperature\", \"precipitation_probability\", \"precipitation\", \"rain\", \"visibility\", \"wind_speed_10m\", \"uv_index\"],\n    daily: [\"weather_code\", \"sunrise\", \"sunset\"],\n    timeformat: \"unixtime\",\n    timezone: \"auto\",\n    past_days: 0,\n    forecast_days: 6\n  };\n  function getRoundedHour(unixTimestamp) {\n    // Create a date object from the Unix timestamp (in seconds)\n    const date = new Date(unixTimestamp * 1000);\n\n    // Get hours, minutes, and seconds\n    const hours = date.getUTCHours() + 6;\n    const minutes = date.getUTCMinutes();\n    const seconds = date.getUTCSeconds();\n\n    // Determine the rounded hour\n    let roundedHour;\n    if (minutes > 31 || seconds > 31) {\n      roundedHour = hours + 1; // Round up to the next hour\n    } else {\n      roundedHour = hours; // Keep the same hour\n    }\n\n    // Return the rounded hour (in 24-hour format)\n    return roundedHour % 24; // Ensure it wraps around at 24\n  }\n\n  // Example usage\n  const unixTime = Math.floor(Date.now() / 1000); // Example Unix timestamp\n  const roundedHour = getRoundedHour(unixTime);\n  const currentTime = Math.floor(Date.now() / 1000);\n  const isoString = new Date(currentTime * 1000).toISOString();\n  useEffect(() => {\n    if (!ipData || !ipData.lat || !ipData.lon) {\n      console.log('Waiting for location data...');\n      return;\n    }\n    const fetchWeather = async () => {\n      try {\n        console.log('Fetching weather data for:', ipData);\n        const url = \"https://api.open-meteo.com/v1/forecast\";\n        const responses = await fetchWeatherApi(url, params);\n        if (!responses || responses.length === 0) {\n          throw new Error('No weather data received');\n        }\n        const range = (start, stop, step) => Array.from({\n          length: (stop - start) / step\n        }, (_, i) => start + i * step);\n        const response = responses[0];\n        const utcOffsetSeconds = response.utcOffsetSeconds();\n        const hourly = response.hourly();\n        const daily = response.daily();\n        if (!hourly || !daily) {\n          throw new Error('Invalid weather data structure');\n        }\n        const weather = {\n          hourly: {\n            time: range(Number(hourly.time()), Number(hourly.timeEnd()), hourly.interval()).map(t => new Date((t + utcOffsetSeconds) * 1000)),\n            temperature2m: hourly.variables(0).valuesArray(),\n            relativeHumidity2m: hourly.variables(1).valuesArray(),\n            apparentTemperature: hourly.variables(2).valuesArray(),\n            precipitationProbability: hourly.variables(3).valuesArray(),\n            precipitation: hourly.variables(4).valuesArray(),\n            rain: hourly.variables(5).valuesArray(),\n            visibility: hourly.variables(6).valuesArray(),\n            windSpeed10m: hourly.variables(7).valuesArray(),\n            uvIndex: hourly.variables(8).valuesArray()\n          },\n          daily: {\n            time: range(Number(daily.time()), Number(daily.timeEnd()), daily.interval()).map(t => new Date((t + utcOffsetSeconds) * 1000)),\n            weatherCode: daily.variables(0).valuesArray(),\n            sunrise: daily.variables(1).valuesArray(),\n            sunset: daily.variables(2).valuesArray()\n          }\n        };\n        setWeatherData(weather);\n        console.log('Weather data successfully loaded');\n      } catch (err) {\n        console.error('Weather fetch error:', err);\n        setError(err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchWeather();\n  }, [ipData]);\n  useEffect(() => {\n    // filter 5days data\n    if (!weatherData) return;\n    const getFivePMData = data => {\n      const result = [];\n\n      // 5 PM index is 17, calculate the indices for 6 days (6 * 24 = 144)\n      const fivePMIndex = roundedHour;\n\n      // Set here day 1 to avoid today's data\n      for (let day = 1; day < 6; day++) {\n        const index = day * 24 + fivePMIndex;\n        result.push({\n          dayName: data.daily.time[day].toLocaleString('default', {\n            weekday: 'short'\n          }),\n          // Get day name\n          date: data.daily.time[day].toLocaleString('default', {\n            day: 'numeric',\n            month: 'long'\n          }),\n          // Get date month\n          temperature: data.hourly.temperature2m[index],\n          // Temperature\n          feelTemperature: data.hourly.apparentTemperature[index] // Feels-like temperature\n        });\n      }\n      return result;\n    };\n    // set 5days Temperature\n    set5DaysData(getFivePMData(weatherData));\n  }, [ipData, weatherData]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full rounded-2xl bg-gray-200 animate-pulse\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-6 rounded-2xl bg-gradient-to-b from-gray-300 to-gray-400\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 gap-4 w-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-8 flex flex-col content-between justify-between\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row justify-evenly\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 w-16 h-16 bg-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left pl-3 space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-300 rounded w-32\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-6 bg-gray-300 rounded w-48\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-12 bg-gray-300 rounded w-24\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-300 rounded w-36\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-300 rounded w-28\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-4 w-full pl-6 space-y-3\",\n            children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-300 rounded\"\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    const selectLocation = location => {\n      setIpData(location);\n      setError(null);\n      setLoading(true);\n    };\n    const locations = [{\n      name: 'Dhaka, Bangladesh',\n      lat: 23.8103,\n      lon: 90.4125,\n      city: 'Dhaka',\n      country: 'Bangladesh',\n      timezone: 'Asia/Dhaka'\n    }, {\n      name: 'New York, USA',\n      lat: 40.7128,\n      lon: -74.0060,\n      city: 'New York',\n      country: 'United States',\n      timezone: 'America/New_York'\n    }, {\n      name: 'London, UK',\n      lat: 51.5074,\n      lon: -0.1278,\n      city: 'London',\n      country: 'United Kingdom',\n      timezone: 'Europe/London'\n    }, {\n      name: 'Los Angeles, USA',\n      lat: 34.0522,\n      lon: -118.2437,\n      city: 'Los Angeles',\n      country: 'United States',\n      timezone: 'America/Los_Angeles'\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full rounded-2xl bg-red-100 border border-red-300\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-6 rounded-2xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-red-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-2\",\n            children: \"Weather Data Unavailable\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm mb-4\",\n            children: [\"Unable to load weather information: \", error.message]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm mb-4\",\n            children: \"Please select a location manually:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2 mb-4\",\n            children: locations.map((location, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => selectLocation(location),\n              className: \"bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors\",\n              children: location.name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.reload(),\n            className: \"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors\",\n            children: \"Retry Auto-Detection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 13\n    }, this);\n  }\n  // Safety check for weather data\n  if (!weatherData || !weatherData.hourly || !weatherData.hourly.temperature2m) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full rounded-2xl bg-yellow-100 border border-yellow-300\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-6 rounded-2xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-yellow-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-2\",\n            children: \"Loading Weather Data...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: \"Please wait while we fetch the latest weather information.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Generate hourly data with safety checks\n  const hourlyData = [];\n  for (let i = roundedHour; i < roundedHour + 12; i++) {\n    const temperature = weatherData.hourly.temperature2m[i];\n    if (temperature !== undefined && temperature !== null) {\n      hourlyData.push(/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex flex-col text-white ${hourlyData.length < 5 ? 'pb-2' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs -mb-1\",\n          children: i > 12 && i < 25 ? `${i - 12}PM ` : `${i > 24 ? i - 24 : i}AM `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-bold\",\n          children: [Math.round(temperature), \"\\xB0C\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 21\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 17\n      }, this));\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full rounded-2xl\",\n    style: background,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-3 py-6 rounded-2xl\",\n      style: {\n        background: \"linear-gradient(-180deg, #01010110, #010101)\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-12 gap-4 w-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-8 flex flex-col content-between justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row justify-evenly\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"w-full\",\n                src: weatherIcon,\n                alt: \"weather icon for sun and cloud\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white text-left pl-3\",\n              children: [/*#__PURE__*/_jsxDEV(CustomClock, {\n                wLong: ipData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-white\",\n                children: `${(ipData === null || ipData === void 0 ? void 0 : ipData.city) || 'Unknown'}, ${(ipData === null || ipData === void 0 ? void 0 : ipData.country) || 'Unknown'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-white text-6xl font-bold\",\n                children: [weatherData.hourly.temperature2m[roundedHour] !== undefined ? Math.round(weatherData.hourly.temperature2m[roundedHour]) : '--', \"\\xB0 C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Feels like:\", \" \", weatherData.hourly.apparentTemperature[roundedHour] !== undefined ? Math.round(weatherData.hourly.apparentTemperature[roundedHour]) : '--', \"\\xB0 C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Humidity: \", weatherData.hourly.relativeHumidity2m[roundedHour] !== undefined ? weatherData.hourly.relativeHumidity2m[roundedHour] : '--', \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pt-6 w-full overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-6\",\n              children: hourlyData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-4 w-full pl-6\",\n          style: {\n            gap: '0px',\n            borderLeft: \"5px solid #ffffff30\"\n          },\n          children: [data5Days && (data5Days === null || data5Days === void 0 ? void 0 : data5Days.map((item, index) => {\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-12 text-white text-left\",\n              style: {\n                marginTop: \"10px\",\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-span-6\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-bold\",\n                  children: item.dayName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-span-6\",\n                style: {\n                  textAlign: \"center\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [Math.round(item.temperature), \"\\xB0C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 33\n            }, this);\n          })), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white text-left mt-8\",\n            children: [\"Wind Speed:\", \" \", weatherData.hourly.windSpeed10m[roundedHour] !== undefined ? Math.round(weatherData.hourly.windSpeed10m[roundedHour]) : '--', \" km/h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white text-left mb-2\",\n            children: [\"Visibility: \", weatherData.hourly.visibility[roundedHour] !== undefined ? (weatherData.hourly.visibility[roundedHour] / 1000).toFixed(1) : '--', \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            className: \"text-red-700 font-bold text-left bg-white px-6 pt-1 pb-2 border-0 rounded-full\",\n            href: trafficUrl,\n            target: \"__blank\",\n            children: \"Live Traffic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 441,\n    columnNumber: 9\n  }, this);\n};\n_s(WeatherData, \"BYaqoHFA1EBTXI4XkJ7RncLua48=\");\n_c = WeatherData;\nexport default WeatherData;\nvar _c;\n$RefreshReg$(_c, \"WeatherData\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "fetchWeatherApi", "CustomClock", "weatherBackground", "weatherIcon", "jsxDEV", "_jsxDEV", "WeatherData", "_s", "weatherData", "setWeatherData", "loading", "setLoading", "error", "setError", "ipData", "setIpData", "data5Days", "set5DaysData", "background", "backgroundImage", "backgroundPosition", "backgroundSize", "fetchLocationData", "process", "env", "NODE_ENV", "console", "log", "lat", "lon", "city", "country", "timezone", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "geoApis", "url", "transform", "data", "latitude", "longitude", "country_name", "time_zone", "_data$time_zone", "parseFloat", "name", "cityName", "countryName", "timeZone", "navigator", "geolocation", "position", "resolve", "getCurrentPosition", "timeout", "enableHighAccuracy", "coords", "Intl", "DateTimeFormat", "resolvedOptions", "geoError", "message", "i", "length", "api", "controller", "AbortController", "timeoutId", "abort", "response", "fetch", "method", "headers", "mode", "signal", "clearTimeout", "ok", "status", "statusText", "json", "transformedData", "isNaN", "err", "warn", "userTimezone", "defaultLocation", "includes", "trafficUrl", "params", "hourly", "daily", "timeformat", "past_days", "forecast_days", "getRoundedHour", "unixTimestamp", "date", "Date", "hours", "getUTCHours", "minutes", "getUTCMinutes", "seconds", "getUTCSeconds", "roundedHour", "unixTime", "Math", "floor", "now", "currentTime", "isoString", "toISOString", "<PERSON><PERSON><PERSON><PERSON>", "responses", "range", "start", "stop", "step", "Array", "from", "utcOffsetSeconds", "weather", "time", "Number", "timeEnd", "interval", "map", "t", "temperature2m", "variables", "valuesArray", "relativeHumidity2m", "apparentTemperature", "precipitationProbability", "precipitation", "rain", "visibility", "windSpeed10m", "uvIndex", "weatherCode", "sunrise", "sunset", "getFivePMData", "result", "fivePMIndex", "day", "index", "push", "day<PERSON><PERSON>", "toLocaleString", "weekday", "month", "temperature", "feelTemperature", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectLocation", "location", "locations", "onClick", "window", "reload", "hourlyData", "undefined", "round", "style", "src", "alt", "wLong", "gap", "borderLeft", "item", "marginTop", "alignItems", "textAlign", "toFixed", "href", "target", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/weatherAndTime/WeatherData.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { fetchWeather<PERSON><PERSON> } from \"openmeteo\";\r\nimport CustomClock from \"./CustomClock\";\r\nimport weatherBackground from \"../../assets/images/\";\r\nimport weatherIcon from \"../../assets/images/icon-sun-cloud.png\";\r\n\r\nconst WeatherData = () => {\r\n    const [weatherData, setWeatherData] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [ipData, setIpData] = useState(false);\r\n    const [data5Days, set5DaysData] = useState(false);\r\n    const background = {\r\n        backgroundImage: `url(${weatherBackground})`,\r\n        backgroundPosition: 'center',\r\n        backgroundSize: 'cover',\r\n    }\r\n    // Fetch IP data with multiple API fallbacks\r\n    useEffect(() => {\r\n        const fetchLocationData = async () => {\r\n            // For development, use a fixed location to avoid CORS issues\r\n            if (process.env.NODE_ENV === 'development') {\r\n                console.log('Development mode: Using fixed location');\r\n                setIpData({\r\n                    lat: 23.8103,\r\n                    lon: 90.4125,\r\n                    city: 'Dhaka',\r\n                    country: 'Bangladesh',\r\n                    timezone: 'Asia/Dhaka'\r\n                });\r\n                return;\r\n            }\r\n\r\n            // For production, add a timeout to ensure we don't wait too long\r\n            const timeoutPromise = new Promise((_, reject) => {\r\n                setTimeout(() => reject(new Error('API timeout')), 10000); // 10 second timeout\r\n            });\r\n\r\n           \r\n            const geoApis = [\r\n                {\r\n                    url: 'https://reallyfreegeoip.org/json/',\r\n                    transform: (data) => ({\r\n                        lat: data.latitude,\r\n                        lon: data.longitude,\r\n                        city: data.city,\r\n                        country: data.country_name,\r\n                        timezone: data.time_zone\r\n                    })\r\n                },\r\n                {\r\n                    url: 'https://freegeoip.tech/json/',\r\n                    transform: (data) => ({\r\n                        lat: data.latitude,\r\n                        lon: data.longitude,\r\n                        city: data.city,\r\n                        country: data.country_name,\r\n                        timezone: data.time_zone\r\n                    })\r\n                },\r\n                {\r\n                    url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free',\r\n                    transform: (data) => ({\r\n                        lat: parseFloat(data.latitude),\r\n                        lon: parseFloat(data.longitude),\r\n                        city: data.city,\r\n                        country: data.country_name,\r\n                        timezone: data.time_zone?.name || data.timezone\r\n                    })\r\n                },\r\n                {\r\n                    url: 'https://freeipapi.com/api/json',\r\n                    transform: (data) => ({\r\n                        lat: data.latitude,\r\n                        lon: data.longitude,\r\n                        city: data.cityName,\r\n                        country: data.countryName,\r\n                        timezone: data.timeZone\r\n                    })\r\n                }\r\n            ];\r\n\r\n            // First try browser geolocation if available\r\n            if (navigator.geolocation) {\r\n                try {\r\n                    const position = await new Promise((resolve, reject) => {\r\n                        navigator.geolocation.getCurrentPosition(resolve, reject, {\r\n                            timeout: 5000,\r\n                            enableHighAccuracy: false\r\n                        });\r\n                    });\r\n\r\n                    console.log('Using browser geolocation');\r\n                    setIpData({\r\n                        lat: position.coords.latitude,\r\n                        lon: position.coords.longitude,\r\n                        city: 'Current Location',\r\n                        country: 'Current Location',\r\n                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone\r\n                    });\r\n                    return;\r\n                } catch (geoError) {\r\n                    console.log('Browser geolocation failed, trying IP APIs:', geoError.message);\r\n                }\r\n            }\r\n\r\n            // Try each API until one works\r\n            for (let i = 0; i < geoApis.length; i++) {\r\n                const api = geoApis[i];\r\n                try {\r\n                    console.log(`Trying API ${i + 1}/${geoApis.length}: ${api.url}`);\r\n\r\n                    const controller = new AbortController();\r\n                    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout per API\r\n\r\n                    const response = await fetch(api.url, {\r\n                        method: 'GET',\r\n                        headers: {\r\n                            'Accept': 'application/json',\r\n                            'User-Agent': 'Mozilla/5.0 (compatible; WeatherApp/1.0)',\r\n                        },\r\n                        mode: 'cors',\r\n                        signal: controller.signal\r\n                    });\r\n\r\n                    clearTimeout(timeoutId);\r\n\r\n                    if (!response.ok) {\r\n                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n                    }\r\n\r\n                    const data = await response.json();\r\n                    console.log(`API ${i + 1} Response:`, data);\r\n\r\n                    // Transform data to consistent format\r\n                    const transformedData = api.transform(data);\r\n\r\n                    // Validate required fields\r\n                    if (transformedData.lat && transformedData.lon &&\r\n                        !isNaN(transformedData.lat) && !isNaN(transformedData.lon)) {\r\n                        console.log(`✅ Successfully fetched location data from API ${i + 1}:`, transformedData);\r\n                        setIpData(transformedData);\r\n                        return; // Success, exit the loop\r\n                    } else {\r\n                        throw new Error('Invalid or incomplete location data received');\r\n                    }\r\n                } catch (err) {\r\n                    console.warn(`❌ API ${i + 1} failed (${api.url}):`, err.message);\r\n\r\n                    // Special handling for the lead's recommended APIs\r\n                    if (i < 2) { \r\n                        console.warn(`⚠️  Lead's recommended API ${i + 1} failed. This might need attention.`);\r\n                    }\r\n\r\n                    // Continue to next API\r\n                }\r\n            }\r\n\r\n            // If all APIs fail, try a simple approach with user's timezone\r\n            console.error('All geolocation APIs failed, using timezone-based location');\r\n\r\n            // Get user's timezone and set approximate location\r\n            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n            let defaultLocation = {\r\n                lat: 40.7128,\r\n                lon: -74.0060,\r\n                city: 'New York',\r\n                country: 'United States',\r\n                timezone: 'America/New_York'\r\n            };\r\n\r\n            // Set location based on common timezones\r\n            if (userTimezone.includes('Asia/Dhaka')) {\r\n                defaultLocation = {\r\n                    lat: 23.8103,\r\n                    lon: 90.4125,\r\n                    city: 'Dhaka',\r\n                    country: 'Bangladesh',\r\n                    timezone: 'Asia/Dhaka'\r\n                };\r\n            } else if (userTimezone.includes('Europe/London')) {\r\n                defaultLocation = {\r\n                    lat: 51.5074,\r\n                    lon: -0.1278,\r\n                    city: 'London',\r\n                    country: 'United Kingdom',\r\n                    timezone: 'Europe/London'\r\n                };\r\n            } else if (userTimezone.includes('America/Los_Angeles')) {\r\n                defaultLocation = {\r\n                    lat: 34.0522,\r\n                    lon: -118.2437,\r\n                    city: 'Los Angeles',\r\n                    country: 'United States',\r\n                    timezone: 'America/Los_Angeles'\r\n                };\r\n            }\r\n\r\n            console.log('Using timezone-based location:', defaultLocation);\r\n            setIpData(defaultLocation);\r\n        };\r\n\r\n        fetchLocationData();\r\n    }, []);\r\n    // traffic url generate\r\n    const trafficUrl = `https://www.google.com/maps/@${ipData?.lat},${ipData?.lon}`;\r\n    const params = {\r\n        latitude: ipData?.lat,\r\n        longitude: ipData?.lon,\r\n        hourly: [\r\n            \"temperature_2m\", \"relative_humidity_2m\", \"apparent_temperature\", \"precipitation_probability\", \"precipitation\", \"rain\", \"visibility\", \"wind_speed_10m\", \"uv_index\",\r\n        ],\r\n        daily: [\"weather_code\", \"sunrise\", \"sunset\"],\r\n        timeformat: \"unixtime\",\r\n        timezone: \"auto\",\r\n        past_days: 0,\r\n        forecast_days: 6,\r\n    };\r\n    function getRoundedHour(unixTimestamp) {\r\n        // Create a date object from the Unix timestamp (in seconds)\r\n        const date = new Date(unixTimestamp * 1000);\r\n\r\n        // Get hours, minutes, and seconds\r\n        const hours = date.getUTCHours() + 6;\r\n        const minutes = date.getUTCMinutes();\r\n        const seconds = date.getUTCSeconds();\r\n\r\n        // Determine the rounded hour\r\n        let roundedHour;\r\n        if (minutes > 31 || seconds > 31) {\r\n            roundedHour = hours + 1; // Round up to the next hour\r\n        } else {\r\n            roundedHour = hours; // Keep the same hour\r\n        }\r\n\r\n        // Return the rounded hour (in 24-hour format)\r\n        return roundedHour % 24; // Ensure it wraps around at 24\r\n    }\r\n\r\n    // Example usage\r\n    const unixTime = Math.floor(Date.now() / 1000); // Example Unix timestamp\r\n    const roundedHour = getRoundedHour(unixTime);\r\n\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n    const isoString = new Date(currentTime * 1000).toISOString();\r\n\r\n    useEffect(() => {\r\n        if (!ipData || !ipData.lat || !ipData.lon) {\r\n            console.log('Waiting for location data...');\r\n            return;\r\n        }\r\n\r\n        const fetchWeather = async () => {\r\n            try {\r\n                console.log('Fetching weather data for:', ipData);\r\n                const url = \"https://api.open-meteo.com/v1/forecast\";\r\n                const responses = await fetchWeatherApi(url, params);\r\n\r\n                if (!responses || responses.length === 0) {\r\n                    throw new Error('No weather data received');\r\n                }\r\n\r\n                const range = (start, stop, step) =>\r\n                    Array.from({ length: (stop - start) / step }, (_, i) => start + i * step);\r\n                const response = responses[0];\r\n\r\n                const utcOffsetSeconds = response.utcOffsetSeconds();\r\n                const hourly = response.hourly();\r\n                const daily = response.daily();\r\n\r\n                if (!hourly || !daily) {\r\n                    throw new Error('Invalid weather data structure');\r\n                }\r\n\r\n                const weather = {\r\n                    hourly: {\r\n                        time: range(\r\n                            Number(hourly.time()),\r\n                            Number(hourly.timeEnd()),\r\n                            hourly.interval()\r\n                        ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),\r\n                        temperature2m: hourly.variables(0).valuesArray(),\r\n                        relativeHumidity2m: hourly.variables(1).valuesArray(),\r\n                        apparentTemperature: hourly.variables(2).valuesArray(),\r\n                        precipitationProbability: hourly.variables(3).valuesArray(),\r\n                        precipitation: hourly.variables(4).valuesArray(),\r\n                        rain: hourly.variables(5).valuesArray(),\r\n                        visibility: hourly.variables(6).valuesArray(),\r\n                        windSpeed10m: hourly.variables(7).valuesArray(),\r\n                        uvIndex: hourly.variables(8).valuesArray(),\r\n                    },\r\n                    daily: {\r\n                        time: range(\r\n                            Number(daily.time()),\r\n                            Number(daily.timeEnd()),\r\n                            daily.interval()\r\n                        ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),\r\n                        weatherCode: daily.variables(0).valuesArray(),\r\n                        sunrise: daily.variables(1).valuesArray(),\r\n                        sunset: daily.variables(2).valuesArray(),\r\n                    },\r\n                };\r\n\r\n                setWeatherData(weather);\r\n                console.log('Weather data successfully loaded');\r\n            } catch (err) {\r\n                console.error('Weather fetch error:', err);\r\n                setError(err);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n        fetchWeather();\r\n    }, [ipData]);\r\n\r\n    useEffect(() => {\r\n        // filter 5days data\r\n        if (!weatherData) return;\r\n        const getFivePMData = (data) => {\r\n            const result = [];\r\n\r\n            // 5 PM index is 17, calculate the indices for 6 days (6 * 24 = 144)\r\n            const fivePMIndex = roundedHour;\r\n\r\n            // Set here day 1 to avoid today's data\r\n            for (let day = 1; day < 6; day++) {\r\n                const index = day * 24 + fivePMIndex;\r\n                result.push({\r\n                    dayName: data.daily.time[day].toLocaleString('default', { weekday: 'short' }), // Get day name\r\n                    date: data.daily.time[day].toLocaleString('default', { day: 'numeric', month: 'long' }), // Get date month\r\n                    temperature: data.hourly.temperature2m[index], // Temperature\r\n                    feelTemperature: data.hourly.apparentTemperature[index], // Feels-like temperature\r\n                });\r\n            }\r\n\r\n            return result;\r\n        };\r\n        // set 5days Temperature\r\n        set5DaysData(getFivePMData(weatherData))\r\n    }, [ipData, weatherData]);\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"w-full rounded-2xl bg-gray-200 animate-pulse\">\r\n                <div className=\"px-3 py-6 rounded-2xl bg-gradient-to-b from-gray-300 to-gray-400\">\r\n                    <div className=\"grid grid-cols-12 gap-4 w-100\">\r\n                        <div className=\"col-span-8 flex flex-col content-between justify-between\">\r\n                            <div className=\"flex flex-row justify-evenly\">\r\n                                <div className=\"mt-8 w-16 h-16 bg-gray-300 rounded\"></div>\r\n                                <div className=\"text-left pl-3 space-y-2\">\r\n                                    <div className=\"h-4 bg-gray-300 rounded w-32\"></div>\r\n                                    <div className=\"h-6 bg-gray-300 rounded w-48\"></div>\r\n                                    <div className=\"h-12 bg-gray-300 rounded w-24\"></div>\r\n                                    <div className=\"h-4 bg-gray-300 rounded w-36\"></div>\r\n                                    <div className=\"h-4 bg-gray-300 rounded w-28\"></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"col-span-4 w-full pl-6 space-y-3\">\r\n                            {[...Array(5)].map((_, i) => (\r\n                                <div key={i} className=\"h-4 bg-gray-300 rounded\"></div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        const selectLocation = (location) => {\r\n            setIpData(location);\r\n            setError(null);\r\n            setLoading(true);\r\n        };\r\n\r\n        const locations = [\r\n            { name: 'Dhaka, Bangladesh', lat: 23.8103, lon: 90.4125, city: 'Dhaka', country: 'Bangladesh', timezone: 'Asia/Dhaka' },\r\n            { name: 'New York, USA', lat: 40.7128, lon: -74.0060, city: 'New York', country: 'United States', timezone: 'America/New_York' },\r\n            { name: 'London, UK', lat: 51.5074, lon: -0.1278, city: 'London', country: 'United Kingdom', timezone: 'Europe/London' },\r\n            { name: 'Los Angeles, USA', lat: 34.0522, lon: -118.2437, city: 'Los Angeles', country: 'United States', timezone: 'America/Los_Angeles' },\r\n        ];\r\n\r\n        return (\r\n            <div className=\"w-full rounded-2xl bg-red-100 border border-red-300\">\r\n                <div className=\"px-3 py-6 rounded-2xl\">\r\n                    <div className=\"text-center text-red-700\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Weather Data Unavailable</h3>\r\n                        <p className=\"text-sm mb-4\">Unable to load weather information: {error.message}</p>\r\n                        <p className=\"text-sm mb-4\">Please select a location manually:</p>\r\n                        <div className=\"grid grid-cols-2 gap-2 mb-4\">\r\n                            {locations.map((location, index) => (\r\n                                <button\r\n                                    key={index}\r\n                                    onClick={() => selectLocation(location)}\r\n                                    className=\"bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors\"\r\n                                >\r\n                                    {location.name}\r\n                                </button>\r\n                            ))}\r\n                        </div>\r\n                        <button\r\n                            onClick={() => window.location.reload()}\r\n                            className=\"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors\"\r\n                        >\r\n                            Retry Auto-Detection\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n    // Safety check for weather data\r\n    if (!weatherData || !weatherData.hourly || !weatherData.hourly.temperature2m) {\r\n        return (\r\n            <div className=\"w-full rounded-2xl bg-yellow-100 border border-yellow-300\">\r\n                <div className=\"px-3 py-6 rounded-2xl\">\r\n                    <div className=\"text-center text-yellow-700\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Loading Weather Data...</h3>\r\n                        <p className=\"text-sm\">Please wait while we fetch the latest weather information.</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Generate hourly data with safety checks\r\n    const hourlyData = [];\r\n    for (let i = roundedHour; i < roundedHour + 12; i++) {\r\n        const temperature = weatherData.hourly.temperature2m[i];\r\n        if (temperature !== undefined && temperature !== null) {\r\n            hourlyData.push(\r\n                <div key={i} className={`flex flex-col text-white ${hourlyData.length < 5 ? 'pb-2' : ''}`}>\r\n                    <p className=\"text-xs -mb-1\">{i > 12 && i < 25 ? `${(i - 12)}PM ` : `${i > 24 ? (i - 24) : i}AM `}</p>\r\n                    <p className=\"font-bold\">{Math.round(temperature)}°C</p>\r\n                </div>\r\n            );\r\n        }\r\n    }\r\n    return (\r\n        <div className=\"w-full rounded-2xl\" style={background}>\r\n            <div className=\"px-3 py-6 rounded-2xl\" style={{ background: \"linear-gradient(-180deg, #01010110, #010101)\" }}>\r\n                <div className=\"grid grid-cols-12 gap-4 w-100\" >\r\n                    <div className=\"col-span-8 flex flex-col content-between justify-between\">\r\n                        <div className=\"flex flex-row justify-evenly\">\r\n                            <div className=\"mt-8\">\r\n                                <img className=\"w-full\" src={weatherIcon} alt=\"weather icon for sun and cloud\" />\r\n                            </div>\r\n                            <div className=\"text-white text-left pl-3\">\r\n                                <CustomClock wLong={ipData} />\r\n                                <h5 className=\"text-white\">{`${ipData?.city || 'Unknown'}, ${ipData?.country || 'Unknown'}`}</h5>\r\n                                <h1 className=\"text-white text-6xl font-bold\">\r\n                                    {weatherData.hourly.temperature2m[roundedHour] !== undefined\r\n                                        ? Math.round(weatherData.hourly.temperature2m[roundedHour])\r\n                                        : '--'}° C\r\n                                </h1>\r\n                                <p>\r\n                                    Feels like:{\" \"}\r\n                                    {weatherData.hourly.apparentTemperature[roundedHour] !== undefined\r\n                                        ? Math.round(weatherData.hourly.apparentTemperature[roundedHour])\r\n                                        : '--'}° C\r\n                                </p>\r\n                                <p>\r\n                                    Humidity: {weatherData.hourly.relativeHumidity2m[roundedHour] !== undefined\r\n                                        ? weatherData.hourly.relativeHumidity2m[roundedHour]\r\n                                        : '--'}%\r\n                                </p>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"pt-6 w-full overflow-x-auto\">\r\n                            <div className=\"grid grid-cols-6\">\r\n                                {hourlyData}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"col-span-4 w-full pl-6\" style={{ gap: '0px', borderLeft: \"5px solid #ffffff30\", }}>\r\n                        {data5Days && data5Days?.map((item, index) => {\r\n                            return (\r\n                                <div className=\"grid grid-cols-12 text-white text-left\" key={index} style={{ marginTop: \"10px\", alignItems: 'center' }}>\r\n                                    <div className=\"col-span-6\">\r\n\r\n                                        {/* {item.date}{\" \"} */}\r\n\r\n                                        <p className=\"font-bold\">\r\n                                            {item.dayName}\r\n                                        </p>\r\n                                    </div>\r\n                                    <div className=\"col-span-6\" style={{ textAlign: \"center\" }}>\r\n                                        <p>\r\n                                            {Math.round(item.temperature)}°C\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n                            )\r\n                        })\r\n                        }\r\n                        <p className=\"text-white text-left mt-8\">\r\n                            Wind Speed:{\" \"}\r\n                            {weatherData.hourly.windSpeed10m[roundedHour] !== undefined\r\n                                ? Math.round(weatherData.hourly.windSpeed10m[roundedHour])\r\n                                : '--'} km/h\r\n                        </p>\r\n                        <p className=\"text-white text-left mb-2\">\r\n                            Visibility: {weatherData.hourly.visibility[roundedHour] !== undefined\r\n                                ? (weatherData.hourly.visibility[roundedHour] / 1000).toFixed(1)\r\n                                : '--'} km\r\n                        </p>\r\n                        <a className=\"text-red-700 font-bold text-left bg-white px-6 pt-1 pb-2 border-0 rounded-full\" href={trafficUrl} target=\"__blank\">Live Traffic</a>\r\n                    </div>\r\n                </div>\r\n                {/* <div className=\"pt-6 w-full overflow-x-auto\">\r\n                    <div className=\"grid grid-cols-12\">\r\n                        {hourlyData}\r\n                    </div>\r\n                </div> */}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default WeatherData;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,WAAW;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,WAAW,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMmB,UAAU,GAAG;IACfC,eAAe,EAAE,OAAOjB,iBAAiB,GAAG;IAC5CkB,kBAAkB,EAAE,QAAQ;IAC5BC,cAAc,EAAE;EACpB,CAAC;EACD;EACAvB,SAAS,CAAC,MAAM;IACZ,MAAMwB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MAClC;MACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QACxCC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrDZ,SAAS,CAAC;UACNa,GAAG,EAAE,OAAO;UACZC,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,OAAO;UACbC,OAAO,EAAE,YAAY;UACrBC,QAAQ,EAAE;QACd,CAAC,CAAC;QACF;MACJ;;MAEA;MACA,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;QAC9CC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC;MAGF,MAAMC,OAAO,GAAG,CACZ;QACIC,GAAG,EAAE,mCAAmC;QACxCC,SAAS,EAAGC,IAAI,KAAM;UAClBd,GAAG,EAAEc,IAAI,CAACC,QAAQ;UAClBd,GAAG,EAAEa,IAAI,CAACE,SAAS;UACnBd,IAAI,EAAEY,IAAI,CAACZ,IAAI;UACfC,OAAO,EAAEW,IAAI,CAACG,YAAY;UAC1Bb,QAAQ,EAAEU,IAAI,CAACI;QACnB,CAAC;MACL,CAAC,EACD;QACIN,GAAG,EAAE,8BAA8B;QACnCC,SAAS,EAAGC,IAAI,KAAM;UAClBd,GAAG,EAAEc,IAAI,CAACC,QAAQ;UAClBd,GAAG,EAAEa,IAAI,CAACE,SAAS;UACnBd,IAAI,EAAEY,IAAI,CAACZ,IAAI;UACfC,OAAO,EAAEW,IAAI,CAACG,YAAY;UAC1Bb,QAAQ,EAAEU,IAAI,CAACI;QACnB,CAAC;MACL,CAAC,EACD;QACIN,GAAG,EAAE,gDAAgD;QACrDC,SAAS,EAAGC,IAAI;UAAA,IAAAK,eAAA;UAAA,OAAM;YAClBnB,GAAG,EAAEoB,UAAU,CAACN,IAAI,CAACC,QAAQ,CAAC;YAC9Bd,GAAG,EAAEmB,UAAU,CAACN,IAAI,CAACE,SAAS,CAAC;YAC/Bd,IAAI,EAAEY,IAAI,CAACZ,IAAI;YACfC,OAAO,EAAEW,IAAI,CAACG,YAAY;YAC1Bb,QAAQ,EAAE,EAAAe,eAAA,GAAAL,IAAI,CAACI,SAAS,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,IAAI,KAAIP,IAAI,CAACV;UAC3C,CAAC;QAAA;MACL,CAAC,EACD;QACIQ,GAAG,EAAE,gCAAgC;QACrCC,SAAS,EAAGC,IAAI,KAAM;UAClBd,GAAG,EAAEc,IAAI,CAACC,QAAQ;UAClBd,GAAG,EAAEa,IAAI,CAACE,SAAS;UACnBd,IAAI,EAAEY,IAAI,CAACQ,QAAQ;UACnBnB,OAAO,EAAEW,IAAI,CAACS,WAAW;UACzBnB,QAAQ,EAAEU,IAAI,CAACU;QACnB,CAAC;MACL,CAAC,CACJ;;MAED;MACA,IAAIC,SAAS,CAACC,WAAW,EAAE;QACvB,IAAI;UACA,MAAMC,QAAQ,GAAG,MAAM,IAAIrB,OAAO,CAAC,CAACsB,OAAO,EAAEpB,MAAM,KAAK;YACpDiB,SAAS,CAACC,WAAW,CAACG,kBAAkB,CAACD,OAAO,EAAEpB,MAAM,EAAE;cACtDsB,OAAO,EAAE,IAAI;cACbC,kBAAkB,EAAE;YACxB,CAAC,CAAC;UACN,CAAC,CAAC;UAEFjC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;UACxCZ,SAAS,CAAC;YACNa,GAAG,EAAE2B,QAAQ,CAACK,MAAM,CAACjB,QAAQ;YAC7Bd,GAAG,EAAE0B,QAAQ,CAACK,MAAM,CAAChB,SAAS;YAC9Bd,IAAI,EAAE,kBAAkB;YACxBC,OAAO,EAAE,kBAAkB;YAC3BC,QAAQ,EAAE6B,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACX;UACtD,CAAC,CAAC;UACF;QACJ,CAAC,CAAC,OAAOY,QAAQ,EAAE;UACftC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEqC,QAAQ,CAACC,OAAO,CAAC;QAChF;MACJ;;MAEA;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,OAAO,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,MAAME,GAAG,GAAG7B,OAAO,CAAC2B,CAAC,CAAC;QACtB,IAAI;UACAxC,OAAO,CAACC,GAAG,CAAC,cAAcuC,CAAC,GAAG,CAAC,IAAI3B,OAAO,CAAC4B,MAAM,KAAKC,GAAG,CAAC5B,GAAG,EAAE,CAAC;UAEhE,MAAM6B,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;UACxC,MAAMC,SAAS,GAAGlC,UAAU,CAAC,MAAMgC,UAAU,CAACG,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;UAE9D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACN,GAAG,CAAC5B,GAAG,EAAE;YAClCmC,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACL,QAAQ,EAAE,kBAAkB;cAC5B,YAAY,EAAE;YAClB,CAAC;YACDC,IAAI,EAAE,MAAM;YACZC,MAAM,EAAET,UAAU,CAACS;UACvB,CAAC,CAAC;UAEFC,YAAY,CAACR,SAAS,CAAC;UAEvB,IAAI,CAACE,QAAQ,CAACO,EAAE,EAAE;YACd,MAAM,IAAI1C,KAAK,CAAC,QAAQmC,QAAQ,CAACQ,MAAM,KAAKR,QAAQ,CAACS,UAAU,EAAE,CAAC;UACtE;UAEA,MAAMxC,IAAI,GAAG,MAAM+B,QAAQ,CAACU,IAAI,CAAC,CAAC;UAClCzD,OAAO,CAACC,GAAG,CAAC,OAAOuC,CAAC,GAAG,CAAC,YAAY,EAAExB,IAAI,CAAC;;UAE3C;UACA,MAAM0C,eAAe,GAAGhB,GAAG,CAAC3B,SAAS,CAACC,IAAI,CAAC;;UAE3C;UACA,IAAI0C,eAAe,CAACxD,GAAG,IAAIwD,eAAe,CAACvD,GAAG,IAC1C,CAACwD,KAAK,CAACD,eAAe,CAACxD,GAAG,CAAC,IAAI,CAACyD,KAAK,CAACD,eAAe,CAACvD,GAAG,CAAC,EAAE;YAC5DH,OAAO,CAACC,GAAG,CAAC,iDAAiDuC,CAAC,GAAG,CAAC,GAAG,EAAEkB,eAAe,CAAC;YACvFrE,SAAS,CAACqE,eAAe,CAAC;YAC1B,OAAO,CAAC;UACZ,CAAC,MAAM;YACH,MAAM,IAAI9C,KAAK,CAAC,8CAA8C,CAAC;UACnE;QACJ,CAAC,CAAC,OAAOgD,GAAG,EAAE;UACV5D,OAAO,CAAC6D,IAAI,CAAC,SAASrB,CAAC,GAAG,CAAC,YAAYE,GAAG,CAAC5B,GAAG,IAAI,EAAE8C,GAAG,CAACrB,OAAO,CAAC;;UAEhE;UACA,IAAIC,CAAC,GAAG,CAAC,EAAE;YACPxC,OAAO,CAAC6D,IAAI,CAAC,8BAA8BrB,CAAC,GAAG,CAAC,qCAAqC,CAAC;UAC1F;;UAEA;QACJ;MACJ;;MAEA;MACAxC,OAAO,CAACd,KAAK,CAAC,4DAA4D,CAAC;;MAE3E;MACA,MAAM4E,YAAY,GAAG3B,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACX,QAAQ;MACrE,IAAIqC,eAAe,GAAG;QAClB7D,GAAG,EAAE,OAAO;QACZC,GAAG,EAAE,CAAC,OAAO;QACbC,IAAI,EAAE,UAAU;QAChBC,OAAO,EAAE,eAAe;QACxBC,QAAQ,EAAE;MACd,CAAC;;MAED;MACA,IAAIwD,YAAY,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;QACrCD,eAAe,GAAG;UACd7D,GAAG,EAAE,OAAO;UACZC,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,OAAO;UACbC,OAAO,EAAE,YAAY;UACrBC,QAAQ,EAAE;QACd,CAAC;MACL,CAAC,MAAM,IAAIwD,YAAY,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAE;QAC/CD,eAAe,GAAG;UACd7D,GAAG,EAAE,OAAO;UACZC,GAAG,EAAE,CAAC,MAAM;UACZC,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,gBAAgB;UACzBC,QAAQ,EAAE;QACd,CAAC;MACL,CAAC,MAAM,IAAIwD,YAAY,CAACE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;QACrDD,eAAe,GAAG;UACd7D,GAAG,EAAE,OAAO;UACZC,GAAG,EAAE,CAAC,QAAQ;UACdC,IAAI,EAAE,aAAa;UACnBC,OAAO,EAAE,eAAe;UACxBC,QAAQ,EAAE;QACd,CAAC;MACL;MAEAN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8D,eAAe,CAAC;MAC9D1E,SAAS,CAAC0E,eAAe,CAAC;IAC9B,CAAC;IAEDnE,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAMqE,UAAU,GAAG,gCAAgC7E,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEc,GAAG,IAAId,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEe,GAAG,EAAE;EAC/E,MAAM+D,MAAM,GAAG;IACXjD,QAAQ,EAAE7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEc,GAAG;IACrBgB,SAAS,EAAE9B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEe,GAAG;IACtBgE,MAAM,EAAE,CACJ,gBAAgB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,2BAA2B,EAAE,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,CACrK;IACDC,KAAK,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC5CC,UAAU,EAAE,UAAU;IACtB/D,QAAQ,EAAE,MAAM;IAChBgE,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE;EACnB,CAAC;EACD,SAASC,cAAcA,CAACC,aAAa,EAAE;IACnC;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,aAAa,GAAG,IAAI,CAAC;;IAE3C;IACA,MAAMG,KAAK,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC,GAAG,CAAC;IACpC,MAAMC,OAAO,GAAGJ,IAAI,CAACK,aAAa,CAAC,CAAC;IACpC,MAAMC,OAAO,GAAGN,IAAI,CAACO,aAAa,CAAC,CAAC;;IAEpC;IACA,IAAIC,WAAW;IACf,IAAIJ,OAAO,GAAG,EAAE,IAAIE,OAAO,GAAG,EAAE,EAAE;MAC9BE,WAAW,GAAGN,KAAK,GAAG,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACHM,WAAW,GAAGN,KAAK,CAAC,CAAC;IACzB;;IAEA;IACA,OAAOM,WAAW,GAAG,EAAE,CAAC,CAAC;EAC7B;;EAEA;EACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACV,IAAI,CAACW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EAChD,MAAMJ,WAAW,GAAGV,cAAc,CAACW,QAAQ,CAAC;EAE5C,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACV,IAAI,CAACW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;EACjD,MAAME,SAAS,GAAG,IAAIb,IAAI,CAACY,WAAW,GAAG,IAAI,CAAC,CAACE,WAAW,CAAC,CAAC;EAE5DrH,SAAS,CAAC,MAAM;IACZ,IAAI,CAACgB,MAAM,IAAI,CAACA,MAAM,CAACc,GAAG,IAAI,CAACd,MAAM,CAACe,GAAG,EAAE;MACvCH,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACJ;IAEA,MAAMyF,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACA1F,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEb,MAAM,CAAC;QACjD,MAAM0B,GAAG,GAAG,wCAAwC;QACpD,MAAM6E,SAAS,GAAG,MAAMrH,eAAe,CAACwC,GAAG,EAAEoD,MAAM,CAAC;QAEpD,IAAI,CAACyB,SAAS,IAAIA,SAAS,CAAClD,MAAM,KAAK,CAAC,EAAE;UACtC,MAAM,IAAI7B,KAAK,CAAC,0BAA0B,CAAC;QAC/C;QAEA,MAAMgF,KAAK,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,IAAI,KAC5BC,KAAK,CAACC,IAAI,CAAC;UAAExD,MAAM,EAAE,CAACqD,IAAI,GAAGD,KAAK,IAAIE;QAAK,CAAC,EAAE,CAACtF,CAAC,EAAE+B,CAAC,KAAKqD,KAAK,GAAGrD,CAAC,GAAGuD,IAAI,CAAC;QAC7E,MAAMhD,QAAQ,GAAG4C,SAAS,CAAC,CAAC,CAAC;QAE7B,MAAMO,gBAAgB,GAAGnD,QAAQ,CAACmD,gBAAgB,CAAC,CAAC;QACpD,MAAM/B,MAAM,GAAGpB,QAAQ,CAACoB,MAAM,CAAC,CAAC;QAChC,MAAMC,KAAK,GAAGrB,QAAQ,CAACqB,KAAK,CAAC,CAAC;QAE9B,IAAI,CAACD,MAAM,IAAI,CAACC,KAAK,EAAE;UACnB,MAAM,IAAIxD,KAAK,CAAC,gCAAgC,CAAC;QACrD;QAEA,MAAMuF,OAAO,GAAG;UACZhC,MAAM,EAAE;YACJiC,IAAI,EAAER,KAAK,CACPS,MAAM,CAAClC,MAAM,CAACiC,IAAI,CAAC,CAAC,CAAC,EACrBC,MAAM,CAAClC,MAAM,CAACmC,OAAO,CAAC,CAAC,CAAC,EACxBnC,MAAM,CAACoC,QAAQ,CAAC,CACpB,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAK,IAAI9B,IAAI,CAAC,CAAC8B,CAAC,GAAGP,gBAAgB,IAAI,IAAI,CAAC,CAAC;YACrDQ,aAAa,EAAEvC,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAChDC,kBAAkB,EAAE1C,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACrDE,mBAAmB,EAAE3C,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACtDG,wBAAwB,EAAE5C,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC3DI,aAAa,EAAE7C,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAChDK,IAAI,EAAE9C,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACvCM,UAAU,EAAE/C,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC7CO,YAAY,EAAEhD,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC/CQ,OAAO,EAAEjD,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAC7C,CAAC;UACDxC,KAAK,EAAE;YACHgC,IAAI,EAAER,KAAK,CACPS,MAAM,CAACjC,KAAK,CAACgC,IAAI,CAAC,CAAC,CAAC,EACpBC,MAAM,CAACjC,KAAK,CAACkC,OAAO,CAAC,CAAC,CAAC,EACvBlC,KAAK,CAACmC,QAAQ,CAAC,CACnB,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAK,IAAI9B,IAAI,CAAC,CAAC8B,CAAC,GAAGP,gBAAgB,IAAI,IAAI,CAAC,CAAC;YACrDmB,WAAW,EAAEjD,KAAK,CAACuC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC7CU,OAAO,EAAElD,KAAK,CAACuC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACzCW,MAAM,EAAEnD,KAAK,CAACuC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAC3C;QACJ,CAAC;QAED7H,cAAc,CAACoH,OAAO,CAAC;QACvBnG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACnD,CAAC,CAAC,OAAO2D,GAAG,EAAE;QACV5D,OAAO,CAACd,KAAK,CAAC,sBAAsB,EAAE0E,GAAG,CAAC;QAC1CzE,QAAQ,CAACyE,GAAG,CAAC;MACjB,CAAC,SAAS;QACN3E,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IACDyG,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAACtG,MAAM,CAAC,CAAC;EAEZhB,SAAS,CAAC,MAAM;IACZ;IACA,IAAI,CAACU,WAAW,EAAE;IAClB,MAAM0I,aAAa,GAAIxG,IAAI,IAAK;MAC5B,MAAMyG,MAAM,GAAG,EAAE;;MAEjB;MACA,MAAMC,WAAW,GAAGxC,WAAW;;MAE/B;MACA,KAAK,IAAIyC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;QAC9B,MAAMC,KAAK,GAAGD,GAAG,GAAG,EAAE,GAAGD,WAAW;QACpCD,MAAM,CAACI,IAAI,CAAC;UACRC,OAAO,EAAE9G,IAAI,CAACoD,KAAK,CAACgC,IAAI,CAACuB,GAAG,CAAC,CAACI,cAAc,CAAC,SAAS,EAAE;YAAEC,OAAO,EAAE;UAAQ,CAAC,CAAC;UAAE;UAC/EtD,IAAI,EAAE1D,IAAI,CAACoD,KAAK,CAACgC,IAAI,CAACuB,GAAG,CAAC,CAACI,cAAc,CAAC,SAAS,EAAE;YAAEJ,GAAG,EAAE,SAAS;YAAEM,KAAK,EAAE;UAAO,CAAC,CAAC;UAAE;UACzFC,WAAW,EAAElH,IAAI,CAACmD,MAAM,CAACuC,aAAa,CAACkB,KAAK,CAAC;UAAE;UAC/CO,eAAe,EAAEnH,IAAI,CAACmD,MAAM,CAAC2C,mBAAmB,CAACc,KAAK,CAAC,CAAE;QAC7D,CAAC,CAAC;MACN;MAEA,OAAOH,MAAM;IACjB,CAAC;IACD;IACAlI,YAAY,CAACiI,aAAa,CAAC1I,WAAW,CAAC,CAAC;EAC5C,CAAC,EAAE,CAACM,MAAM,EAAEN,WAAW,CAAC,CAAC;EAEzB,IAAIE,OAAO,EAAE;IACT,oBACIL,OAAA;MAAKyJ,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eACzD1J,OAAA;QAAKyJ,SAAS,EAAC,kEAAkE;QAAAC,QAAA,eAC7E1J,OAAA;UAAKyJ,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC1C1J,OAAA;YAAKyJ,SAAS,EAAC,0DAA0D;YAAAC,QAAA,eACrE1J,OAAA;cAAKyJ,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBACzC1J,OAAA;gBAAKyJ,SAAS,EAAC;cAAoC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D9J,OAAA;gBAAKyJ,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACrC1J,OAAA;kBAAKyJ,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpD9J,OAAA;kBAAKyJ,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpD9J,OAAA;kBAAKyJ,SAAS,EAAC;gBAA+B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrD9J,OAAA;kBAAKyJ,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpD9J,OAAA;kBAAKyJ,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN9J,OAAA;YAAKyJ,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAC5C,CAAC,GAAGrC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACQ,GAAG,CAAC,CAAC/F,CAAC,EAAE+B,CAAC,kBACpB7D,OAAA;cAAayJ,SAAS,EAAC;YAAyB,GAAtC5F,CAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA2C,CACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIvJ,KAAK,EAAE;IACP,MAAMwJ,cAAc,GAAIC,QAAQ,IAAK;MACjCtJ,SAAS,CAACsJ,QAAQ,CAAC;MACnBxJ,QAAQ,CAAC,IAAI,CAAC;MACdF,UAAU,CAAC,IAAI,CAAC;IACpB,CAAC;IAED,MAAM2J,SAAS,GAAG,CACd;MAAErH,IAAI,EAAE,mBAAmB;MAAErB,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,OAAO;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,YAAY;MAAEC,QAAQ,EAAE;IAAa,CAAC,EACvH;MAAEiB,IAAI,EAAE,eAAe;MAAErB,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC,OAAO;MAAEC,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,eAAe;MAAEC,QAAQ,EAAE;IAAmB,CAAC,EAChI;MAAEiB,IAAI,EAAE,YAAY;MAAErB,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC,MAAM;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,gBAAgB;MAAEC,QAAQ,EAAE;IAAgB,CAAC,EACxH;MAAEiB,IAAI,EAAE,kBAAkB;MAAErB,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC,QAAQ;MAAEC,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAE,eAAe;MAAEC,QAAQ,EAAE;IAAsB,CAAC,CAC7I;IAED,oBACI3B,OAAA;MAAKyJ,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAChE1J,OAAA;QAAKyJ,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eAClC1J,OAAA;UAAKyJ,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACrC1J,OAAA;YAAIyJ,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxE9J,OAAA;YAAGyJ,SAAS,EAAC,cAAc;YAAAC,QAAA,GAAC,sCAAoC,EAACnJ,KAAK,CAACqD,OAAO;UAAA;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF9J,OAAA;YAAGyJ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClE9J,OAAA;YAAKyJ,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EACvCO,SAAS,CAACpC,GAAG,CAAC,CAACmC,QAAQ,EAAEf,KAAK,kBAC3BjJ,OAAA;cAEIkK,OAAO,EAAEA,CAAA,KAAMH,cAAc,CAACC,QAAQ,CAAE;cACxCP,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EAE/FM,QAAQ,CAACpH;YAAI,GAJTqG,KAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKN,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9J,OAAA;YACIkK,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACH,QAAQ,CAACI,MAAM,CAAC,CAAE;YACxCX,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EACzF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EACA;EACA,IAAI,CAAC3J,WAAW,IAAI,CAACA,WAAW,CAACqF,MAAM,IAAI,CAACrF,WAAW,CAACqF,MAAM,CAACuC,aAAa,EAAE;IAC1E,oBACI/H,OAAA;MAAKyJ,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eACtE1J,OAAA;QAAKyJ,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eAClC1J,OAAA;UAAKyJ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBACxC1J,OAAA;YAAIyJ,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvE9J,OAAA;YAAGyJ,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;;EAEA;EACA,MAAMO,UAAU,GAAG,EAAE;EACrB,KAAK,IAAIxG,CAAC,GAAG0C,WAAW,EAAE1C,CAAC,GAAG0C,WAAW,GAAG,EAAE,EAAE1C,CAAC,EAAE,EAAE;IACjD,MAAM0F,WAAW,GAAGpJ,WAAW,CAACqF,MAAM,CAACuC,aAAa,CAAClE,CAAC,CAAC;IACvD,IAAI0F,WAAW,KAAKe,SAAS,IAAIf,WAAW,KAAK,IAAI,EAAE;MACnDc,UAAU,CAACnB,IAAI,cACXlJ,OAAA;QAAayJ,SAAS,EAAE,4BAA4BY,UAAU,CAACvG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,EAAG;QAAA4F,QAAA,gBACtF1J,OAAA;UAAGyJ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE7F,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,EAAE,GAAG,GAAIA,CAAC,GAAG,EAAE,KAAM,GAAG,GAAGA,CAAC,GAAG,EAAE,GAAIA,CAAC,GAAG,EAAE,GAAIA,CAAC;QAAK;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtG9J,OAAA;UAAGyJ,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAEjD,IAAI,CAAC8D,KAAK,CAAChB,WAAW,CAAC,EAAC,OAAE;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA,GAFlDjG,CAAC;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGN,CACT,CAAC;IACL;EACJ;EACA,oBACI9J,OAAA;IAAKyJ,SAAS,EAAC,oBAAoB;IAACe,KAAK,EAAE3J,UAAW;IAAA6I,QAAA,eAClD1J,OAAA;MAAKyJ,SAAS,EAAC,uBAAuB;MAACe,KAAK,EAAE;QAAE3J,UAAU,EAAE;MAA+C,CAAE;MAAA6I,QAAA,eACzG1J,OAAA;QAAKyJ,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC1C1J,OAAA;UAAKyJ,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACrE1J,OAAA;YAAKyJ,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBACzC1J,OAAA;cAAKyJ,SAAS,EAAC,MAAM;cAAAC,QAAA,eACjB1J,OAAA;gBAAKyJ,SAAS,EAAC,QAAQ;gBAACgB,GAAG,EAAE3K,WAAY;gBAAC4K,GAAG,EAAC;cAAgC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACN9J,OAAA;cAAKyJ,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACtC1J,OAAA,CAACJ,WAAW;gBAAC+K,KAAK,EAAElK;cAAO;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B9J,OAAA;gBAAIyJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE,GAAG,CAAAjJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,IAAI,KAAI,SAAS,KAAK,CAAAhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,OAAO,KAAI,SAAS;cAAE;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjG9J,OAAA;gBAAIyJ,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,GACxCvJ,WAAW,CAACqF,MAAM,CAACuC,aAAa,CAACxB,WAAW,CAAC,KAAK+D,SAAS,GACtD7D,IAAI,CAAC8D,KAAK,CAACpK,WAAW,CAACqF,MAAM,CAACuC,aAAa,CAACxB,WAAW,CAAC,CAAC,GACzD,IAAI,EAAC,QACf;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9J,OAAA;gBAAA0J,QAAA,GAAG,aACY,EAAC,GAAG,EACdvJ,WAAW,CAACqF,MAAM,CAAC2C,mBAAmB,CAAC5B,WAAW,CAAC,KAAK+D,SAAS,GAC5D7D,IAAI,CAAC8D,KAAK,CAACpK,WAAW,CAACqF,MAAM,CAAC2C,mBAAmB,CAAC5B,WAAW,CAAC,CAAC,GAC/D,IAAI,EAAC,QACf;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ9J,OAAA;gBAAA0J,QAAA,GAAG,YACW,EAACvJ,WAAW,CAACqF,MAAM,CAAC0C,kBAAkB,CAAC3B,WAAW,CAAC,KAAK+D,SAAS,GACrEnK,WAAW,CAACqF,MAAM,CAAC0C,kBAAkB,CAAC3B,WAAW,CAAC,GAClD,IAAI,EAAC,GACf;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN9J,OAAA;YAAKyJ,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eACxC1J,OAAA;cAAKyJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC5BW;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9J,OAAA;UAAKyJ,SAAS,EAAC,wBAAwB;UAACe,KAAK,EAAE;YAAEI,GAAG,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAuB,CAAE;UAAAnB,QAAA,GAC7F/I,SAAS,KAAIA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkH,GAAG,CAAC,CAACiD,IAAI,EAAE7B,KAAK,KAAK;YAC1C,oBACIjJ,OAAA;cAAKyJ,SAAS,EAAC,wCAAwC;cAAae,KAAK,EAAE;gBAAEO,SAAS,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAtB,QAAA,gBACnH1J,OAAA;gBAAKyJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAIvB1J,OAAA;kBAAGyJ,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACnBoB,IAAI,CAAC3B;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9J,OAAA;gBAAKyJ,SAAS,EAAC,YAAY;gBAACe,KAAK,EAAE;kBAAES,SAAS,EAAE;gBAAS,CAAE;gBAAAvB,QAAA,eACvD1J,OAAA;kBAAA0J,QAAA,GACKjD,IAAI,CAAC8D,KAAK,CAACO,IAAI,CAACvB,WAAW,CAAC,EAAC,OAClC;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAbmDb,KAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAc7D,CAAC;UAEd,CAAC,CAAC,gBAEF9J,OAAA;YAAGyJ,SAAS,EAAC,2BAA2B;YAAAC,QAAA,GAAC,aAC1B,EAAC,GAAG,EACdvJ,WAAW,CAACqF,MAAM,CAACgD,YAAY,CAACjC,WAAW,CAAC,KAAK+D,SAAS,GACrD7D,IAAI,CAAC8D,KAAK,CAACpK,WAAW,CAACqF,MAAM,CAACgD,YAAY,CAACjC,WAAW,CAAC,CAAC,GACxD,IAAI,EAAC,OACf;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9J,OAAA;YAAGyJ,SAAS,EAAC,2BAA2B;YAAAC,QAAA,GAAC,cACzB,EAACvJ,WAAW,CAACqF,MAAM,CAAC+C,UAAU,CAAChC,WAAW,CAAC,KAAK+D,SAAS,GAC/D,CAACnK,WAAW,CAACqF,MAAM,CAAC+C,UAAU,CAAChC,WAAW,CAAC,GAAG,IAAI,EAAE2E,OAAO,CAAC,CAAC,CAAC,GAC9D,IAAI,EAAC,KACf;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9J,OAAA;YAAGyJ,SAAS,EAAC,gFAAgF;YAAC0B,IAAI,EAAE7F,UAAW;YAAC8F,MAAM,EAAC,SAAS;YAAA1B,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAML;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC5J,EAAA,CAhgBID,WAAW;AAAAoL,EAAA,GAAXpL,WAAW;AAkgBjB,eAAeA,WAAW;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}