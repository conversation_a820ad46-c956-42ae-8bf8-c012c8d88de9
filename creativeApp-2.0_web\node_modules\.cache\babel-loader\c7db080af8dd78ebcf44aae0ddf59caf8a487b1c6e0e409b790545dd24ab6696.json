{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{Eye,EyeOff,X,Shield,User,Lock,Building,Users,AlertCircle,CheckCircle,Wand2}from\"lucide-react\";import{useCreatePasswordManagerMutation,useUpdatePasswordManagerMutation}from\"../../features/api/passwordManagerApi\";import{useGetDepartmentsWithTeamsQuery}from\"../../features/api/departmentApi\";import{useGetTeamsWithDepartmentsQuery}from\"../../features/api/teamApi\";// Main component for the Add Password Card form\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AddPasswordCardForm=_ref=>{let{onCancel,generatedPassword,passwordStrength,editData=null,// For editing existing password\nonSuccess,// Callback after successful save\ndepartmentsData:propDepartmentsData=null,teamsData:propTeamsData=null}=_ref;// Use props if provided, otherwise fallback to API hooks\nconst{data:departmentsDataApi,isLoading:isDepartmentsLoading,error:departmentsError}=useGetDepartmentsWithTeamsQuery(undefined,{skip:!!propDepartmentsData});const{data:teamsDataApi,isLoading:isTeamsLoading,error:teamsError}=useGetTeamsWithDepartmentsQuery(undefined,{skip:!!propTeamsData});const departmentsData=propDepartmentsData||departmentsDataApi;const teamsData=propTeamsData||teamsDataApi;const[createPasswordManager,{isLoading:isCreating}]=useCreatePasswordManagerMutation();const[updatePasswordManager,{isLoading:isUpdating}]=useUpdatePasswordManagerMutation();// console.clear();\n// console.log(departmentsData);\n// State to hold form data\nconst[formData,setFormData]=useState({password_title:\"\",username:\"\",password:\"\",department_id:\"\",team_id:\"\"});// Initialize form data for editing or reset for new entries\nuseEffect(()=>{if(editData){setFormData({password_title:editData.password_title||editData.title||\"\",username:editData.username||\"\",password:\"\",// Don't pre-fill password for security\ndepartment_id:editData.department_id?String(editData.department_id):\"\",team_id:editData.team_id?String(editData.team_id):\"\"});}else{// FIXED: Reset form completely when not editing (new entry)\nsetFormData({password_title:\"\",username:\"\",password:\"\",department_id:\"\",team_id:\"\"});setErrors({});setFormError(\"\");setShowPassword(false);}},[editData]);// State for showing/hiding the password, individual field errors, and a general form error\nconst[showPassword,setShowPassword]=useState(false);const[errors,setErrors]=useState({});const[formError,setFormError]=useState(\"\");const[isLoading,setIsLoading]=useState(false);const[focusedField,setFocusedField]=useState(null);// Effect to update the form's password field when a new password is generated\nuseEffect(()=>{if(generatedPassword){setFormData(prev=>({...prev,password:generatedPassword,strength:passwordStrength}));}},[generatedPassword,passwordStrength]);// Handles input changes for all form fields\nconst handleInputChange=e=>{const{name,value}=e.target;// Reset team selection when the department changes\nif(name===\"department_id\"){setFormData(prev=>({...prev,[name]:value,team_id:\"\"}));}else{setFormData(prev=>({...prev,[name]:value}));}// Clear the error for a field when the user starts typing\nif(errors[name]){setErrors(prev=>({...prev,[name]:\"\"}));}// Clear form error when user starts typing\nif(formError){setFormError(\"\");}};// Calculates the strength of a given password\nconst calculatePasswordStrength=password=>{if(!password)return\"Weak Password\";let score=0;if(password.length>=12)score+=2;else if(password.length>=8)score+=1;if(/[a-z]/.test(password))score+=1;if(/[A-Z]/.test(password))score+=1;if(/[0-9]/.test(password))score+=1;if(/[^A-Za-z0-9]/.test(password))score+=1;if(password.length>=16)score+=1;if(score>=6)return\"Strong Password\";if(score>=4)return\"Moderate Password\";return\"Weak Password\";};// Returns Tailwind CSS classes based on password strength for styling\nconst getStrengthColor=strength=>{switch(strength){case\"Strong Password\":return\"bg-emerald-50 text-emerald-700 border-emerald-200\";case\"Moderate Password\":return\"bg-amber-50 text-amber-700 border-amber-200\";case\"Weak Password\":return\"bg-red-50 text-red-700 border-red-200\";default:return\"bg-gray-50 text-gray-600 border-gray-200\";}};// Get strength progress and color\nconst getStrengthProgress=strength=>{switch(strength){case\"Strong Password\":return{width:\"100%\",color:\"bg-emerald-500\"};case\"Moderate Password\":return{width:\"66%\",color:\"bg-amber-500\"};case\"Weak Password\":return{width:\"33%\",color:\"bg-red-500\"};default:return{width:\"0%\",color:\"bg-gray-300\"};}};// Validates the form fields before submission\nconst validateForm=()=>{const newErrors={};if(!formData.password_title.trim())newErrors.password_title=\"Platform title is required\";if(!formData.username.trim())newErrors.username=\"Username is required\";if(!formData.password.trim())newErrors.password=\"Password is required\";setErrors(newErrors);return Object.keys(newErrors).length===0;};// Handles the form submission\nconst handleSubmit=async e=>{e.preventDefault();setFormError(\"\");setIsLoading(true);if(validateForm()){try{var _result,_result$data;const submitData={password_title:formData.password_title,username:formData.username,password:formData.password,department_id:formData.department_id?parseInt(formData.department_id):null,team_id:formData.team_id?parseInt(formData.team_id):null};let newPasswordId=null;let result;if(editData){// Update existing password\nresult=await updatePasswordManager({id:editData.id,...submitData}).unwrap();}else{// Create new password\nresult=await createPasswordManager(submitData).unwrap();}// Pass the new or updated item's ID to the success handler\nnewPasswordId=(_result=result)===null||_result===void 0?void 0:(_result$data=_result.data)===null||_result$data===void 0?void 0:_result$data.id;// FIXED: Reset form after successful submission\nsetFormData({password_title:\"\",username:\"\",password:\"\",department_id:\"\",team_id:\"\"});setErrors({});setFormError(\"\");setShowPassword(false);// FIXED: Call success callback to refresh data and close modal\n// The success callback will handle the toast notification\nif(onSuccess){onSuccess(newPasswordId);}}catch(error){var _error$data;console.error(\"Error saving password:\",error);setFormError((error===null||error===void 0?void 0:(_error$data=error.data)===null||_error$data===void 0?void 0:_error$data.message)||\"Failed to save password. Please try again.\");}}else{setFormError(\"Please fill out all required fields before saving.\");}setIsLoading(false);};// Sets the form password to the generated password\nconst useGeneratedPassword=()=>{if(generatedPassword){setFormData(prev=>({...prev,password:generatedPassword,strength:passwordStrength}));}};const togglePasswordVisibility=()=>setShowPassword(!showPassword);const hasErrors=Object.keys(errors).length>0;return/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 sm:space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-1.5 sm:p-2 bg-white/20 rounded-lg\",children:/*#__PURE__*/_jsx(Shield,{className:\"w-4 h-4 sm:w-6 sm:h-6\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-left\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg sm:text-xl font-semibold\",children:\"Add New Password\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-100 text-xs sm:text-sm\",children:\"Secure your credentials with us\"})]})})]}),/*#__PURE__*/_jsx(\"button\",{onClick:onCancel,className:\"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\",children:/*#__PURE__*/_jsx(X,{className:\"w-4 h-4 sm:w-5 sm:h-5\"})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\",children:[formError&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-left font-medium text-red-800 text-sm sm:text-base\",children:\"Action Required\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-red-700 text-xs sm:text-sm\",children:formError})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3 sm:space-y-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${hasErrors?\"min-h-[300px] sm:h-[340px]\":\"min-h-[250px] sm:h-[280px]\"}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-3 sm:mb-4\",children:[/*#__PURE__*/_jsx(Building,{className:\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-gray-800 text-sm sm:text-base\",children:\"Organization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3 sm:space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",children:[\"Department \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"select\",{name:\"department_id\",value:formData.department_id,onChange:handleInputChange,onFocus:()=>setFocusedField(\"department_id\"),onBlur:()=>setFocusedField(null),className:`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none focus:ring-0 text-sm sm:text-base ${errors.department_id?\"border-red-300 focus:border-red-500\":focusedField===\"department_id\"?\"border-primary-500 shadow-lg shadow-primary/10-100\":\"border-gray-200 focus:border-primary hover:border-gray-300\"}`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Department\"}),isDepartmentsLoading&&/*#__PURE__*/_jsx(\"option\",{disabled:true,children:\"Loading...\"}),departmentsError&&/*#__PURE__*/_jsx(\"option\",{disabled:true,children:\"Error loading departments\"}),departmentsData===null||departmentsData===void 0?void 0:departmentsData.map(dept=>/*#__PURE__*/_jsx(\"option\",{value:dept.id,children:dept.name},dept.id))]}),errors.department_id&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"w-3 h-3 sm:w-4 sm:h-4 mr-1\"}),errors.department_id]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",children:[\"Team \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"select\",{name:\"team_id\",value:formData.team_id,onChange:handleInputChange,onFocus:()=>setFocusedField(\"team_id\"),onBlur:()=>setFocusedField(null),disabled:!formData.department_id,className:`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${!formData.department_id?\"bg-gray-100 border-gray-200 cursor-not-allowed\":errors.team_id?\"border-red-300 focus:border-red-500\":focusedField===\"team_id\"?\"border-primary-500 shadow-lg shadow-primary-100\":\"border-gray-200 focus:border-primary-500 hover:border-gray-300\"}`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Team\"}),isTeamsLoading&&/*#__PURE__*/_jsx(\"option\",{disabled:true,children:\"Loading...\"}),teamsError&&/*#__PURE__*/_jsx(\"option\",{disabled:true,children:\"Error loading teams\"}),formData.department_id&&(teamsData===null||teamsData===void 0?void 0:teamsData.filter(team=>{var _team$department_ids;return(_team$department_ids=team.department_ids)===null||_team$department_ids===void 0?void 0:_team$department_ids.includes(parseInt(formData.department_id));}).map(team=>/*#__PURE__*/_jsx(\"option\",{value:team.id,children:team.name},team.id)))]}),errors.team_id&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"w-3 h-3 sm:w-4 sm:h-4 mr-1\"}),errors.team_id]}),!formData.department_id&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-gray-500 flex items-center\",children:[/*#__PURE__*/_jsx(Users,{className:\"w-3 h-3 sm:w-4 sm:h-4 mr-1\"}),\"Please select a department first\"]})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3 sm:space-y-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${hasErrors?\"min-h-[300px] sm:h-[340px]\":\"min-h-[250px] sm:h-[280px]\"}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-3 sm:mb-4\",children:[/*#__PURE__*/_jsx(User,{className:\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-gray-800 text-sm sm:text-base\",children:\"Account Details\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col h-full space-y-3 sm:space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",children:[\"Platform Title \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"password_title\",value:formData.password_title,onChange:handleInputChange,onFocus:()=>setFocusedField(\"password_title\"),onBlur:()=>setFocusedField(null),placeholder:\"e.g., Gmail, GitHub, AWS Console\",className:`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${errors.password_title?\"border-red-300 focus:border-red-500\":focusedField===\"password_title\"?\"border-primary-500 shadow-lg shadow-primary/10-100\":\"border-gray-200 focus:border-primary hover:border-gray-300\"}`}),errors.password_title&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"w-3 h-3 sm:w-4 sm:h-4 mr-1\"}),errors.password_title]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",children:[\"Username \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"username\",value:formData.username,onChange:handleInputChange,onFocus:()=>setFocusedField(\"username\"),onBlur:()=>setFocusedField(null),placeholder:\"Enter username or email\",className:`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${errors.username?\"border-red-300 focus:border-red-500\":focusedField===\"username\"?\"border-primary-500 shadow-lg shadow-primary/10-100\":\"border-gray-200 focus:border-primary hover:border-gray-300\"}`}),errors.username&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"w-3 h-3 sm:w-4 sm:h-4 mr-1\"}),errors.username]})]})]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 sm:mt-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-3 sm:mb-4\",children:[/*#__PURE__*/_jsx(Lock,{className:\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-gray-800 text-sm sm:text-base\",children:\"Security\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\",children:[\"Password \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"input\",{type:showPassword?\"text\":\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,onFocus:()=>setFocusedField(\"password\"),onBlur:()=>setFocusedField(null),placeholder:\"Enter password\",className:`w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-20 sm:pr-32 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${errors.password?\"border-red-300 focus:border-red-500\":focusedField===\"password\"?\"border-primary-500 shadow-lg shadow-primary/10-100\":\"border-gray-200 focus:border-primary-500 hover:border-gray-300\"}`}),/*#__PURE__*/_jsxs(\"div\",{className:\"absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 flex items-center space-x-0.5 sm:space-x-1\",children:[/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:useGeneratedPassword,disabled:!generatedPassword,className:`px-2 sm:px-3 py-1 sm:py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-0.5 sm:space-x-1 ${generatedPassword?\"bg-primary/10-100 text-white-700 hover:bg-primary-200\":\"bg-gray-100 text-gray-400 cursor-not-allowed\"}`,title:\"Use generated password\",children:[/*#__PURE__*/_jsx(Wand2,{className:\"w-2.5 h-2.5 sm:w-3 sm:h-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:\"Use\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:togglePasswordVisibility,className:\"p-1 sm:p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200\",children:showPassword?/*#__PURE__*/_jsx(EyeOff,{className:\"w-3 h-3 sm:w-4 sm:h-4\"}):/*#__PURE__*/_jsx(Eye,{className:\"w-3 h-3 sm:w-4 sm:h-4\"})})]})]}),errors.password&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"w-3 h-3 sm:w-4 sm:h-4 mr-1\"}),errors.password]}),formData.password&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 sm:mt-4 space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs sm:text-sm font-medium text-gray-700\",children:\"Password Strength\"}),/*#__PURE__*/_jsx(\"span\",{className:`text-xs sm:text-sm font-medium px-2 py-1 rounded-full ${getStrengthColor(formData.strength)}`,children:formData.strength})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-1.5 sm:h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:`h-1.5 sm:h-2 rounded-full transition-all duration-300 ${getStrengthProgress(formData.strength).color}`,style:{width:getStrengthProgress(formData.strength).width}})})]})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-xs sm:text-sm text-gray-500\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-3 h-3 sm:w-4 sm:h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"All fields marked with * are required\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onCancel,className:\"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 font-medium text-sm sm:text-base\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleSubmit,disabled:isLoading||isCreating||isUpdating,className:\"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 bg-primary hover:bg-primary/90 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-500 focus:ring-offset-2 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 sm:space-x-2 text-sm sm:text-base\",children:isLoading||isCreating||isUpdating?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:editData?\"Updating...\":\"Saving...\"}),/*#__PURE__*/_jsx(\"span\",{className:\"sm:hidden\",children:editData?\"Update\":\"Save\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Shield,{className:\"w-3 h-3 sm:w-4 sm:h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:editData?\"Update Password\":\"Save Password\"}),/*#__PURE__*/_jsx(\"span\",{className:\"sm:hidden\",children:editData?\"Update\":\"Save\"})]})})]})]})]})});};export default AddPasswordCardForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Eye", "Eye<PERSON>ff", "X", "Shield", "User", "Lock", "Building", "Users", "AlertCircle", "CheckCircle", "Wand2", "useCreatePasswordManagerMutation", "useUpdatePasswordManagerMutation", "useGetDepartmentsWithTeamsQuery", "useGetTeamsWithDepartmentsQuery", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AddPasswordCardForm", "_ref", "onCancel", "generatedPassword", "passwordStrength", "editData", "onSuccess", "departmentsData", "propDepartmentsData", "teamsData", "propTeamsData", "data", "departmentsDataApi", "isLoading", "isDepartmentsLoading", "error", "departmentsError", "undefined", "skip", "teamsDataApi", "isTeamsLoading", "teamsError", "createPasswordManager", "isCreating", "updatePasswordManager", "isUpdating", "formData", "setFormData", "password_title", "username", "password", "department_id", "team_id", "title", "String", "setErrors", "setFormError", "setShowPassword", "showPassword", "errors", "formError", "setIsLoading", "focusedField", "setFocusedField", "prev", "strength", "handleInputChange", "e", "name", "value", "target", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "getStrengthProgress", "width", "color", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "_result", "_result$data", "submitData", "parseInt", "newPasswordId", "result", "id", "unwrap", "_error$data", "console", "message", "useGeneratedPassword", "togglePasswordVisibility", "hasErrors", "className", "children", "onClick", "onChange", "onFocus", "onBlur", "disabled", "map", "dept", "filter", "team", "_team$department_ids", "department_ids", "includes", "type", "placeholder", "style"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordCardForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport {\n  Eye,\n  EyeOff,\n  X,\n  Shield,\n  User,\n  Lock,\n  Building,\n  Users,\n  AlertCircle,\n  CheckCircle,\n  Wand2,\n} from \"lucide-react\";\nimport {\n  useCreatePasswordManagerMutation,\n  useUpdatePasswordManagerMutation,\n} from \"../../features/api/passwordManagerApi\";\nimport { useGetDepartmentsWithTeamsQuery } from \"../../features/api/departmentApi\";\nimport { useGetTeamsWithDepartmentsQuery } from \"../../features/api/teamApi\";\n\n// Main component for the Add Password Card form\nconst AddPasswordCardForm = ({\n  onCancel,\n  generatedPassword,\n  passwordStrength,\n  editData = null, // For editing existing password\n  onSuccess, // Callback after successful save\n  departmentsData: propDepartmentsData = null,\n  teamsData: propTeamsData = null,\n}) => {\n  // Use props if provided, otherwise fallback to API hooks\n  const { data: departmentsDataApi, isLoading: isDepartmentsLoading, error: departmentsError } = useGetDepartmentsWithTeamsQuery(undefined, { skip: !!propDepartmentsData });\n  const { data: teamsDataApi, isLoading: isTeamsLoading, error: teamsError } = useGetTeamsWithDepartmentsQuery(undefined, { skip: !!propTeamsData });\n  const departmentsData = propDepartmentsData || departmentsDataApi;\n  const teamsData = propTeamsData || teamsDataApi;\n  const [createPasswordManager, { isLoading: isCreating }] =\n    useCreatePasswordManagerMutation();\n  const [updatePasswordManager, { isLoading: isUpdating }] =\n    useUpdatePasswordManagerMutation();\n  // console.clear();\n\n  // console.log(departmentsData);\n\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    password_title: \"\",\n    username: \"\",\n    password: \"\",\n    department_id: \"\",\n    team_id: \"\",\n  });\n\n  // Initialize form data for editing or reset for new entries\n  useEffect(() => {\n    if (editData) {\n      setFormData({\n        password_title: editData.password_title || editData.title || \"\",\n        username: editData.username || \"\",\n        password: \"\", // Don't pre-fill password for security\n        department_id: editData.department_id\n          ? String(editData.department_id)\n          : \"\",\n        team_id: editData.team_id ? String(editData.team_id) : \"\",\n      });\n    } else {\n      // FIXED: Reset form completely when not editing (new entry)\n      setFormData({\n        password_title: \"\",\n        username: \"\",\n        password: \"\",\n        department_id: \"\",\n        team_id: \"\",\n      });\n      setErrors({});\n      setFormError(\"\");\n      setShowPassword(false);\n    }\n  }, [editData]);\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [focusedField, setFocusedField] = useState(null);\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData((prev) => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength,\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === \"department_id\") {\n      setFormData((prev) => ({ ...prev, [name]: value, team_id: \"\" }));\n    } else {\n      setFormData((prev) => ({ ...prev, [name]: value }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors((prev) => ({ ...prev, [name]: \"\" }));\n    }\n\n    // Clear form error when user starts typing\n    if (formError) {\n      setFormError(\"\");\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = (password) => {\n    if (!password) return \"Weak Password\";\n    let score = 0;\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return \"Strong Password\";\n    if (score >= 4) return \"Moderate Password\";\n    return \"Weak Password\";\n  };\n  \n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case \"Strong Password\":\n        return \"bg-emerald-50 text-emerald-700 border-emerald-200\";\n      case \"Moderate Password\":\n        return \"bg-amber-50 text-amber-700 border-amber-200\";\n      case \"Weak Password\":\n        return \"bg-red-50 text-red-700 border-red-200\";\n      default:\n        return \"bg-gray-50 text-gray-600 border-gray-200\";\n    }\n  };\n\n  // Get strength progress and color\n  const getStrengthProgress = (strength) => {\n    switch (strength) {\n      case \"Strong Password\":\n        return { width: \"100%\", color: \"bg-emerald-500\" };\n      case \"Moderate Password\":\n        return { width: \"66%\", color: \"bg-amber-500\" };\n      case \"Weak Password\":\n        return { width: \"33%\", color: \"bg-red-500\" };\n      default:\n        return { width: \"0%\", color: \"bg-gray-300\" };\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.password_title.trim())\n      newErrors.password_title = \"Platform title is required\";\n    if (!formData.username.trim()) newErrors.username = \"Username is required\";\n    if (!formData.password.trim()) newErrors.password = \"Password is required\";\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setFormError(\"\");\n    setIsLoading(true);\n\n    if (validateForm()) {\n      try {\n        const submitData = {\n          password_title: formData.password_title,\n          username: formData.username,\n          password: formData.password,\n          department_id: formData.department_id\n            ? parseInt(formData.department_id)\n            : null,\n          team_id: formData.team_id ? parseInt(formData.team_id) : null,\n        };\n\n        let newPasswordId = null;\n\n        let result;\n        if (editData) {\n          // Update existing password\n          result = await updatePasswordManager({\n            id: editData.id,\n            ...submitData,\n          }).unwrap();\n        } else {\n          // Create new password\n          result = await createPasswordManager(submitData).unwrap();\n        }\n\n        // Pass the new or updated item's ID to the success handler\n        newPasswordId = result?.data?.id;\n\n        // FIXED: Reset form after successful submission\n        setFormData({\n          password_title: \"\",\n          username: \"\",\n          password: \"\",\n          department_id: \"\",\n          team_id: \"\",\n        });\n        setErrors({});\n        setFormError(\"\");\n        setShowPassword(false);\n\n        // FIXED: Call success callback to refresh data and close modal\n        // The success callback will handle the toast notification\n        if (onSuccess) {\n          onSuccess(newPasswordId);\n        }\n      } catch (error) {\n        console.error(\"Error saving password:\", error);\n        setFormError(\n          error?.data?.message || \"Failed to save password. Please try again.\"\n        );\n      }\n    } else {\n      setFormError(\"Please fill out all required fields before saving.\");\n    }\n\n    setIsLoading(false);\n  };\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData((prev) => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength,\n      }));\n    }\n  };\n\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n\n  const hasErrors = Object.keys(errors).length > 0;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4 animate-in fade-in duration-300\">\n      <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\">\n        {/* Header - Responsive */}\n        <div className=\"bg-primary px-3 sm:px-6 py-3 sm:py-4 text-white\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2 sm:space-x-3\">\n              <div className=\"p-1.5 sm:p-2 bg-white/20 rounded-lg\">\n                <Shield className=\"w-4 h-4 sm:w-6 sm:h-6\" />\n              </div>\n              <div>\n                <div className=\"text-left\">\n                  <h2 className=\"text-lg sm:text-xl font-semibold\">\n                    Add New Password\n                  </h2>\n                  <p className=\"text-white-100 text-xs sm:text-sm\">\n                    Secure your credentials with us\n                  </p>\n                </div>\n              </div>\n            </div>\n            <button\n              onClick={onCancel}\n              className=\"p-1.5 sm:p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\"\n            >\n              <X className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Form Content - Responsive */}\n        <div className=\"p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-200px)]\">\n          {/* Error Message - Responsive */}\n          {formError && (\n            <div className=\"mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg sm:rounded-xl flex items-start space-x-2 sm:space-x-3 animate-in slide-in-from-top-2\">\n              <AlertCircle className=\"w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5 flex-shrink-0\" />\n              <div>\n                <p className=\"text-left font-medium text-red-800 text-sm sm:text-base\">\n                  Action Required\n                </p>\n                <p className=\"text-red-700 text-xs sm:text-sm\">{formError}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Responsive Grid Layout */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\n            {/* Department & Team Section - Responsive */}\n            <div className=\"space-y-3 sm:space-y-4\">\n              <div\n                className={`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${\n                  hasErrors\n                    ? \"min-h-[300px] sm:h-[340px]\"\n                    : \"min-h-[250px] sm:h-[280px]\"\n                }`}\n              >\n                <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\n                  <Building className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\n                  <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\n                    Organization\n                  </h3>\n                </div>\n\n                <div className=\"space-y-3 sm:space-y-4\">\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Department <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      name=\"department_id\"\n                      value={formData.department_id}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"department_id\")}\n                      onBlur={() => setFocusedField(null)}\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none focus:ring-0 text-sm sm:text-base ${\n                        errors.department_id\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"department_id\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    >\n                      <option value=\"\">Select Department</option>\n                      {isDepartmentsLoading && <option disabled>Loading...</option>}\n                      {departmentsError && <option disabled>Error loading departments</option>}\n                      {departmentsData?.map((dept) => (\n                        <option key={dept.id} value={dept.id}>\n                          {dept.name}\n                        </option>\n                      ))}\n                    </select>\n                    {errors.department_id && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.department_id}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Team <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      name=\"team_id\"\n                      value={formData.team_id}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"team_id\")}\n                      onBlur={() => setFocusedField(null)}\n                      disabled={!formData.department_id}\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                        !formData.department_id\n                          ? \"bg-gray-100 border-gray-200 cursor-not-allowed\"\n                          : errors.team_id\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"team_id\"\n                          ? \"border-primary-500 shadow-lg shadow-primary-100\"\n                          : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"\n                      }`}\n                    >\n                      <option value=\"\">Select Team</option>\n                      {isTeamsLoading && <option disabled>Loading...</option>}\n                      {teamsError && <option disabled>Error loading teams</option>}\n                      {formData.department_id &&\n                        teamsData\n                          ?.filter((team) =>\n                            team.department_ids?.includes(\n                              parseInt(formData.department_id)\n                            )\n                          )\n                          .map((team) => (\n                            <option key={team.id} value={team.id}>\n                              {team.name}\n                            </option>\n                          ))}\n                    </select>\n                    {errors.team_id && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.team_id}\n                      </p>\n                    )}\n                    {!formData.department_id && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-gray-500 flex items-center\">\n                        <Users className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        Please select a department first\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Platform & Username Section - Responsive */}\n            <div className=\"space-y-3 sm:space-y-4\">\n              <div\n                className={`p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200 ${\n                  hasErrors\n                    ? \"min-h-[300px] sm:h-[340px]\"\n                    : \"min-h-[250px] sm:h-[280px]\"\n                }`}\n              >\n                <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\n                  <User className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\n                  <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\n                    Account Details\n                  </h3>\n                </div>\n\n                <div className=\"flex flex-col h-full space-y-3 sm:space-y-4\">\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Platform Title <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"password_title\"\n                      value={formData.password_title}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"password_title\")}\n                      onBlur={() => setFocusedField(null)}\n                      placeholder=\"e.g., Gmail, GitHub, AWS Console\"\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                        errors.password_title\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"password_title\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    />\n                    {errors.password_title && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.password_title}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                      Username <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"username\"\n                      value={formData.username}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"username\")}\n                      onBlur={() => setFocusedField(null)}\n                      placeholder=\"Enter username or email\"\n                      className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                        errors.username\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"username\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    />\n                    {errors.username && (\n                      <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        {errors.username}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Password Section - Responsive */}\n          <div className=\"mt-4 sm:mt-6\">\n            <div className=\"p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\">\n              <div className=\"flex items-center space-x-2 mb-3 sm:mb-4\">\n                <Lock className=\"w-4 h-4 sm:w-5 sm:h-5 text-gray-600\" />\n                <h3 className=\"font-medium text-gray-800 text-sm sm:text-base\">\n                  Security\n                </h3>\n              </div>\n\n              <div>\n                <label className=\"text-left block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2\">\n                  Password <span className=\"text-red-500\">*</span>\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showPassword ? \"text\" : \"password\"}\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    onFocus={() => setFocusedField(\"password\")}\n                    onBlur={() => setFocusedField(null)}\n                    placeholder=\"Enter password\"\n                    className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-20 sm:pr-32 rounded-lg border-2 transition-all duration-200 focus:outline-none text-sm sm:text-base ${\n                      errors.password\n                        ? \"border-red-300 focus:border-red-500\"\n                        : focusedField === \"password\"\n                        ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                        : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"\n                    }`}\n                  />\n\n                  {/* Responsive Button Container */}\n                  <div className=\"absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 flex items-center space-x-0.5 sm:space-x-1\">\n                    <button\n                      type=\"button\"\n                      onClick={useGeneratedPassword}\n                      disabled={!generatedPassword}\n                      className={`px-2 sm:px-3 py-1 sm:py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-0.5 sm:space-x-1 ${\n                        generatedPassword\n                          ? \"bg-primary/10-100 text-white-700 hover:bg-primary-200\"\n                          : \"bg-gray-100 text-gray-400 cursor-not-allowed\"\n                      }`}\n                      title=\"Use generated password\"\n                    >\n                      <Wand2 className=\"w-2.5 h-2.5 sm:w-3 sm:h-3\" />\n                      <span className=\"hidden sm:inline\">Use</span>\n                    </button>\n\n                    <button\n                      type=\"button\"\n                      onClick={togglePasswordVisibility}\n                      className=\"p-1 sm:p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200\"\n                    >\n                      {showPassword ? (\n                        <EyeOff className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                      ) : (\n                        <Eye className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                      )}\n                    </button>\n                  </div>\n                </div>\n\n                {errors.password && (\n                  <p className=\"mt-1.5 sm:mt-2 text-xs sm:text-sm text-red-600 flex items-center\">\n                    <AlertCircle className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                    {errors.password}\n                  </p>\n                )}\n\n                {/* Password Strength Indicator - Responsive */}\n                {formData.password && (\n                  <div className=\"mt-3 sm:mt-4 space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-xs sm:text-sm font-medium text-gray-700\">\n                        Password Strength\n                      </span>\n                      <span\n                        className={`text-xs sm:text-sm font-medium px-2 py-1 rounded-full ${getStrengthColor(\n                          formData.strength\n                        )}`}\n                      >\n                        {formData.strength}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-1.5 sm:h-2\">\n                      <div\n                        className={`h-1.5 sm:h-2 rounded-full transition-all duration-300 ${\n                          getStrengthProgress(formData.strength).color\n                        }`}\n                        style={{\n                          width: getStrengthProgress(formData.strength).width,\n                        }}\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer - Responsive */}\n        <div className=\"px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0\">\n          <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-gray-500\">\n            <CheckCircle className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n            <span>All fields marked with * are required</span>\n          </div>\n\n          {/* Responsive Button Container */}\n          <div className=\"flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto\">\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 font-medium text-sm sm:text-base\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={handleSubmit}\n              disabled={isLoading || isCreating || isUpdating}\n              className=\"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-2.5 bg-primary hover:bg-primary/90 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-500 focus:ring-offset-2 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 sm:space-x-2 text-sm sm:text-base\"\n            >\n              {isLoading || isCreating || isUpdating ? (\n                <>\n                  <div className=\"w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span className=\"hidden sm:inline\">\n                    {editData ? \"Updating...\" : \"Saving...\"}\n                  </span>\n                  <span className=\"sm:hidden\">\n                    {editData ? \"Update\" : \"Save\"}\n                  </span>\n                </>\n              ) : (\n                <>\n                  <Shield className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                  <span className=\"hidden sm:inline\">\n                    {editData ? \"Update Password\" : \"Save Password\"}\n                  </span>\n                  <span className=\"sm:hidden\">\n                    {editData ? \"Update\" : \"Save\"}\n                  </span>\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddPasswordCardForm;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,MAAM,CACNC,CAAC,CACDC,MAAM,CACNC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,KAAK,CACLC,WAAW,CACXC,WAAW,CACXC,KAAK,KACA,cAAc,CACrB,OACEC,gCAAgC,CAChCC,gCAAgC,KAC3B,uCAAuC,CAC9C,OAASC,+BAA+B,KAAQ,kCAAkC,CAClF,OAASC,+BAA+B,KAAQ,4BAA4B,CAE5E;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EAQtB,IARuB,CAC3BC,QAAQ,CACRC,iBAAiB,CACjBC,gBAAgB,CAChBC,QAAQ,CAAG,IAAI,CAAE;AACjBC,SAAS,CAAE;AACXC,eAAe,CAAEC,mBAAmB,CAAG,IAAI,CAC3CC,SAAS,CAAEC,aAAa,CAAG,IAC7B,CAAC,CAAAT,IAAA,CACC;AACA,KAAM,CAAEU,IAAI,CAAEC,kBAAkB,CAAEC,SAAS,CAAEC,oBAAoB,CAAEC,KAAK,CAAEC,gBAAiB,CAAC,CAAGxB,+BAA+B,CAACyB,SAAS,CAAE,CAAEC,IAAI,CAAE,CAAC,CAACV,mBAAoB,CAAC,CAAC,CAC1K,KAAM,CAAEG,IAAI,CAAEQ,YAAY,CAAEN,SAAS,CAAEO,cAAc,CAAEL,KAAK,CAAEM,UAAW,CAAC,CAAG5B,+BAA+B,CAACwB,SAAS,CAAE,CAAEC,IAAI,CAAE,CAAC,CAACR,aAAc,CAAC,CAAC,CAClJ,KAAM,CAAAH,eAAe,CAAGC,mBAAmB,EAAII,kBAAkB,CACjE,KAAM,CAAAH,SAAS,CAAGC,aAAa,EAAIS,YAAY,CAC/C,KAAM,CAACG,qBAAqB,CAAE,CAAET,SAAS,CAAEU,UAAW,CAAC,CAAC,CACtDjC,gCAAgC,CAAC,CAAC,CACpC,KAAM,CAACkC,qBAAqB,CAAE,CAAEX,SAAS,CAAEY,UAAW,CAAC,CAAC,CACtDlC,gCAAgC,CAAC,CAAC,CACpC;AAEA;AAEA;AACA,KAAM,CAACmC,QAAQ,CAAEC,WAAW,CAAC,CAAGlD,QAAQ,CAAC,CACvCmD,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,EACX,CAAC,CAAC,CAEF;AACAtD,SAAS,CAAC,IAAM,CACd,GAAI2B,QAAQ,CAAE,CACZsB,WAAW,CAAC,CACVC,cAAc,CAAEvB,QAAQ,CAACuB,cAAc,EAAIvB,QAAQ,CAAC4B,KAAK,EAAI,EAAE,CAC/DJ,QAAQ,CAAExB,QAAQ,CAACwB,QAAQ,EAAI,EAAE,CACjCC,QAAQ,CAAE,EAAE,CAAE;AACdC,aAAa,CAAE1B,QAAQ,CAAC0B,aAAa,CACjCG,MAAM,CAAC7B,QAAQ,CAAC0B,aAAa,CAAC,CAC9B,EAAE,CACNC,OAAO,CAAE3B,QAAQ,CAAC2B,OAAO,CAAGE,MAAM,CAAC7B,QAAQ,CAAC2B,OAAO,CAAC,CAAG,EACzD,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAL,WAAW,CAAC,CACVC,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,EACX,CAAC,CAAC,CACFG,SAAS,CAAC,CAAC,CAAC,CAAC,CACbC,YAAY,CAAC,EAAE,CAAC,CAChBC,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAAE,CAAChC,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAACiC,YAAY,CAAED,eAAe,CAAC,CAAG5D,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC8D,MAAM,CAAEJ,SAAS,CAAC,CAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxC,KAAM,CAAC+D,SAAS,CAAEJ,YAAY,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACoC,SAAS,CAAE4B,YAAY,CAAC,CAAGhE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACiE,YAAY,CAAEC,eAAe,CAAC,CAAGlE,QAAQ,CAAC,IAAI,CAAC,CAEtD;AACAC,SAAS,CAAC,IAAM,CACd,GAAIyB,iBAAiB,CAAE,CACrBwB,WAAW,CAAEiB,IAAI,GAAM,CACrB,GAAGA,IAAI,CACPd,QAAQ,CAAE3B,iBAAiB,CAC3B0C,QAAQ,CAAEzC,gBACZ,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAAE,CAACD,iBAAiB,CAAEC,gBAAgB,CAAC,CAAC,CAEzC;AACA,KAAM,CAAA0C,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAEhC;AACA,GAAIF,IAAI,GAAK,eAAe,CAAE,CAC5BrB,WAAW,CAAEiB,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAE,CAACI,IAAI,EAAGC,KAAK,CAAEjB,OAAO,CAAE,EAAG,CAAC,CAAC,CAAC,CAClE,CAAC,IAAM,CACLL,WAAW,CAAEiB,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAE,CAACI,IAAI,EAAGC,KAAM,CAAC,CAAC,CAAC,CACrD,CAEA;AACA,GAAIV,MAAM,CAACS,IAAI,CAAC,CAAE,CAChBb,SAAS,CAAES,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAE,CAACI,IAAI,EAAG,EAAG,CAAC,CAAC,CAAC,CAChD,CAEA;AACA,GAAIR,SAAS,CAAE,CACbJ,YAAY,CAAC,EAAE,CAAC,CAClB,CACF,CAAC,CAED;AACA,KAAM,CAAAe,yBAAyB,CAAIrB,QAAQ,EAAK,CAC9C,GAAI,CAACA,QAAQ,CAAE,MAAO,eAAe,CACrC,GAAI,CAAAsB,KAAK,CAAG,CAAC,CACb,GAAItB,QAAQ,CAACuB,MAAM,EAAI,EAAE,CAAED,KAAK,EAAI,CAAC,CAAC,IACjC,IAAItB,QAAQ,CAACuB,MAAM,EAAI,CAAC,CAAED,KAAK,EAAI,CAAC,CACzC,GAAI,OAAO,CAACE,IAAI,CAACxB,QAAQ,CAAC,CAAEsB,KAAK,EAAI,CAAC,CACtC,GAAI,OAAO,CAACE,IAAI,CAACxB,QAAQ,CAAC,CAAEsB,KAAK,EAAI,CAAC,CACtC,GAAI,OAAO,CAACE,IAAI,CAACxB,QAAQ,CAAC,CAAEsB,KAAK,EAAI,CAAC,CACtC,GAAI,cAAc,CAACE,IAAI,CAACxB,QAAQ,CAAC,CAAEsB,KAAK,EAAI,CAAC,CAC7C,GAAItB,QAAQ,CAACuB,MAAM,EAAI,EAAE,CAAED,KAAK,EAAI,CAAC,CACrC,GAAIA,KAAK,EAAI,CAAC,CAAE,MAAO,iBAAiB,CACxC,GAAIA,KAAK,EAAI,CAAC,CAAE,MAAO,mBAAmB,CAC1C,MAAO,eAAe,CACxB,CAAC,CAGD;AACA,KAAM,CAAAG,gBAAgB,CAAIV,QAAQ,EAAK,CACrC,OAAQA,QAAQ,EACd,IAAK,iBAAiB,CACpB,MAAO,mDAAmD,CAC5D,IAAK,mBAAmB,CACtB,MAAO,6CAA6C,CACtD,IAAK,eAAe,CAClB,MAAO,uCAAuC,CAChD,QACE,MAAO,0CAA0C,CACrD,CACF,CAAC,CAED;AACA,KAAM,CAAAW,mBAAmB,CAAIX,QAAQ,EAAK,CACxC,OAAQA,QAAQ,EACd,IAAK,iBAAiB,CACpB,MAAO,CAAEY,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,gBAAiB,CAAC,CACnD,IAAK,mBAAmB,CACtB,MAAO,CAAED,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,cAAe,CAAC,CAChD,IAAK,eAAe,CAClB,MAAO,CAAED,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,YAAa,CAAC,CAC9C,QACE,MAAO,CAAED,KAAK,CAAE,IAAI,CAAEC,KAAK,CAAE,aAAc,CAAC,CAChD,CACF,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,SAAS,CAAG,CAAC,CAAC,CACpB,GAAI,CAAClC,QAAQ,CAACE,cAAc,CAACiC,IAAI,CAAC,CAAC,CACjCD,SAAS,CAAChC,cAAc,CAAG,4BAA4B,CACzD,GAAI,CAACF,QAAQ,CAACG,QAAQ,CAACgC,IAAI,CAAC,CAAC,CAAED,SAAS,CAAC/B,QAAQ,CAAG,sBAAsB,CAC1E,GAAI,CAACH,QAAQ,CAACI,QAAQ,CAAC+B,IAAI,CAAC,CAAC,CAAED,SAAS,CAAC9B,QAAQ,CAAG,sBAAsB,CAC1EK,SAAS,CAACyB,SAAS,CAAC,CACpB,MAAO,CAAAE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACP,MAAM,GAAK,CAAC,CAC5C,CAAC,CAED;AACA,KAAM,CAAAW,YAAY,CAAG,KAAO,CAAAjB,CAAC,EAAK,CAChCA,CAAC,CAACkB,cAAc,CAAC,CAAC,CAClB7B,YAAY,CAAC,EAAE,CAAC,CAChBK,YAAY,CAAC,IAAI,CAAC,CAElB,GAAIkB,YAAY,CAAC,CAAC,CAAE,CAClB,GAAI,KAAAO,OAAA,CAAAC,YAAA,CACF,KAAM,CAAAC,UAAU,CAAG,CACjBxC,cAAc,CAAEF,QAAQ,CAACE,cAAc,CACvCC,QAAQ,CAAEH,QAAQ,CAACG,QAAQ,CAC3BC,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAC3BC,aAAa,CAAEL,QAAQ,CAACK,aAAa,CACjCsC,QAAQ,CAAC3C,QAAQ,CAACK,aAAa,CAAC,CAChC,IAAI,CACRC,OAAO,CAAEN,QAAQ,CAACM,OAAO,CAAGqC,QAAQ,CAAC3C,QAAQ,CAACM,OAAO,CAAC,CAAG,IAC3D,CAAC,CAED,GAAI,CAAAsC,aAAa,CAAG,IAAI,CAExB,GAAI,CAAAC,MAAM,CACV,GAAIlE,QAAQ,CAAE,CACZ;AACAkE,MAAM,CAAG,KAAM,CAAA/C,qBAAqB,CAAC,CACnCgD,EAAE,CAAEnE,QAAQ,CAACmE,EAAE,CACf,GAAGJ,UACL,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC,CACb,CAAC,IAAM,CACL;AACAF,MAAM,CAAG,KAAM,CAAAjD,qBAAqB,CAAC8C,UAAU,CAAC,CAACK,MAAM,CAAC,CAAC,CAC3D,CAEA;AACAH,aAAa,EAAAJ,OAAA,CAAGK,MAAM,UAAAL,OAAA,kBAAAC,YAAA,CAAND,OAAA,CAAQvD,IAAI,UAAAwD,YAAA,iBAAZA,YAAA,CAAcK,EAAE,CAEhC;AACA7C,WAAW,CAAC,CACVC,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,EACX,CAAC,CAAC,CACFG,SAAS,CAAC,CAAC,CAAC,CAAC,CACbC,YAAY,CAAC,EAAE,CAAC,CAChBC,eAAe,CAAC,KAAK,CAAC,CAEtB;AACA;AACA,GAAI/B,SAAS,CAAE,CACbA,SAAS,CAACgE,aAAa,CAAC,CAC1B,CACF,CAAE,MAAOvD,KAAK,CAAE,KAAA2D,WAAA,CACdC,OAAO,CAAC5D,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CqB,YAAY,CACV,CAAArB,KAAK,SAALA,KAAK,kBAAA2D,WAAA,CAAL3D,KAAK,CAAEJ,IAAI,UAAA+D,WAAA,iBAAXA,WAAA,CAAaE,OAAO,GAAI,4CAC1B,CAAC,CACH,CACF,CAAC,IAAM,CACLxC,YAAY,CAAC,oDAAoD,CAAC,CACpE,CAEAK,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAoC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,GAAI1E,iBAAiB,CAAE,CACrBwB,WAAW,CAAEiB,IAAI,GAAM,CACrB,GAAGA,IAAI,CACPd,QAAQ,CAAE3B,iBAAiB,CAC3B0C,QAAQ,CAAEzC,gBACZ,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAA0E,wBAAwB,CAAGA,CAAA,GAAMzC,eAAe,CAAC,CAACC,YAAY,CAAC,CAErE,KAAM,CAAAyC,SAAS,CAAGjB,MAAM,CAACC,IAAI,CAACxB,MAAM,CAAC,CAACc,MAAM,CAAG,CAAC,CAEhD,mBACE1D,IAAA,QAAKqF,SAAS,CAAC,6HAA6H,CAAAC,QAAA,cAC1IpF,KAAA,QAAKmF,SAAS,CAAC,iMAAiM,CAAAC,QAAA,eAE9MtF,IAAA,QAAKqF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC9DpF,KAAA,QAAKmF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpF,KAAA,QAAKmF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDtF,IAAA,QAAKqF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDtF,IAAA,CAACb,MAAM,EAACkG,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACzC,CAAC,cACNrF,IAAA,QAAAsF,QAAA,cACEpF,KAAA,QAAKmF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBtF,IAAA,OAAIqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,kBAEjD,CAAI,CAAC,cACLtF,IAAA,MAAGqF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,iCAEjD,CAAG,CAAC,EACD,CAAC,CACH,CAAC,EACH,CAAC,cACNtF,IAAA,WACEuF,OAAO,CAAEhF,QAAS,CAClB8E,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cAEpFtF,IAAA,CAACd,CAAC,EAACmG,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACjC,CAAC,EACN,CAAC,CACH,CAAC,cAGNnF,KAAA,QAAKmF,SAAS,CAAC,iFAAiF,CAAAC,QAAA,EAE7FzC,SAAS,eACR3C,KAAA,QAAKmF,SAAS,CAAC,yJAAyJ,CAAAC,QAAA,eACtKtF,IAAA,CAACR,WAAW,EAAC6F,SAAS,CAAC,yDAAyD,CAAE,CAAC,cACnFnF,KAAA,QAAAoF,QAAA,eACEtF,IAAA,MAAGqF,SAAS,CAAC,yDAAyD,CAAAC,QAAA,CAAC,iBAEvE,CAAG,CAAC,cACJtF,IAAA,MAAGqF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAEzC,SAAS,CAAI,CAAC,EAC3D,CAAC,EACH,CACN,cAGD3C,KAAA,QAAKmF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAE7DtF,IAAA,QAAKqF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCpF,KAAA,QACEmF,SAAS,CAAE,yEACTD,SAAS,CACL,4BAA4B,CAC5B,4BAA4B,EAC/B,CAAAE,QAAA,eAEHpF,KAAA,QAAKmF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDtF,IAAA,CAACV,QAAQ,EAAC+F,SAAS,CAAC,qCAAqC,CAAE,CAAC,cAC5DrF,IAAA,OAAIqF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,cAE/D,CAAI,CAAC,EACF,CAAC,cAENpF,KAAA,QAAKmF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpF,KAAA,QAAAoF,QAAA,eACEpF,KAAA,UAAOmF,SAAS,CAAC,6EAA6E,CAAAC,QAAA,EAAC,aAClF,cAAAtF,IAAA,SAAMqF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EAC7C,CAAC,cACRpF,KAAA,WACEmD,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAEvB,QAAQ,CAACK,aAAc,CAC9BoD,QAAQ,CAAErC,iBAAkB,CAC5BsC,OAAO,CAAEA,CAAA,GAAMzC,eAAe,CAAC,eAAe,CAAE,CAChD0C,MAAM,CAAEA,CAAA,GAAM1C,eAAe,CAAC,IAAI,CAAE,CACpCqC,SAAS,CAAE,2IACTzC,MAAM,CAACR,aAAa,CAChB,qCAAqC,CACrCW,YAAY,GAAK,eAAe,CAChC,oDAAoD,CACpD,4DAA4D,EAC/D,CAAAuC,QAAA,eAEHtF,IAAA,WAAQsD,KAAK,CAAC,EAAE,CAAAgC,QAAA,CAAC,mBAAiB,CAAQ,CAAC,CAC1CnE,oBAAoB,eAAInB,IAAA,WAAQ2F,QAAQ,MAAAL,QAAA,CAAC,YAAU,CAAQ,CAAC,CAC5DjE,gBAAgB,eAAIrB,IAAA,WAAQ2F,QAAQ,MAAAL,QAAA,CAAC,2BAAyB,CAAQ,CAAC,CACvE1E,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgF,GAAG,CAAEC,IAAI,eACzB7F,IAAA,WAAsBsD,KAAK,CAAEuC,IAAI,CAAChB,EAAG,CAAAS,QAAA,CAClCO,IAAI,CAACxC,IAAI,EADCwC,IAAI,CAAChB,EAEV,CACT,CAAC,EACI,CAAC,CACRjC,MAAM,CAACR,aAAa,eACnBlC,KAAA,MAAGmF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC7EtF,IAAA,CAACR,WAAW,EAAC6F,SAAS,CAAC,4BAA4B,CAAE,CAAC,CACrDzC,MAAM,CAACR,aAAa,EACpB,CACJ,EACE,CAAC,cAENlC,KAAA,QAAAoF,QAAA,eACEpF,KAAA,UAAOmF,SAAS,CAAC,6EAA6E,CAAAC,QAAA,EAAC,OACxF,cAAAtF,IAAA,SAAMqF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EACvC,CAAC,cACRpF,KAAA,WACEmD,IAAI,CAAC,SAAS,CACdC,KAAK,CAAEvB,QAAQ,CAACM,OAAQ,CACxBmD,QAAQ,CAAErC,iBAAkB,CAC5BsC,OAAO,CAAEA,CAAA,GAAMzC,eAAe,CAAC,SAAS,CAAE,CAC1C0C,MAAM,CAAEA,CAAA,GAAM1C,eAAe,CAAC,IAAI,CAAE,CACpC2C,QAAQ,CAAE,CAAC5D,QAAQ,CAACK,aAAc,CAClCiD,SAAS,CAAE,8HACT,CAACtD,QAAQ,CAACK,aAAa,CACnB,gDAAgD,CAChDQ,MAAM,CAACP,OAAO,CACd,qCAAqC,CACrCU,YAAY,GAAK,SAAS,CAC1B,iDAAiD,CACjD,gEAAgE,EACnE,CAAAuC,QAAA,eAEHtF,IAAA,WAAQsD,KAAK,CAAC,EAAE,CAAAgC,QAAA,CAAC,aAAW,CAAQ,CAAC,CACpC7D,cAAc,eAAIzB,IAAA,WAAQ2F,QAAQ,MAAAL,QAAA,CAAC,YAAU,CAAQ,CAAC,CACtD5D,UAAU,eAAI1B,IAAA,WAAQ2F,QAAQ,MAAAL,QAAA,CAAC,qBAAmB,CAAQ,CAAC,CAC3DvD,QAAQ,CAACK,aAAa,GACrBtB,SAAS,SAATA,SAAS,iBAATA,SAAS,CACLgF,MAAM,CAAEC,IAAI,OAAAC,oBAAA,QAAAA,oBAAA,CACZD,IAAI,CAACE,cAAc,UAAAD,oBAAA,iBAAnBA,oBAAA,CAAqBE,QAAQ,CAC3BxB,QAAQ,CAAC3C,QAAQ,CAACK,aAAa,CACjC,CAAC,EACH,CAAC,CACAwD,GAAG,CAAEG,IAAI,eACR/F,IAAA,WAAsBsD,KAAK,CAAEyC,IAAI,CAAClB,EAAG,CAAAS,QAAA,CAClCS,IAAI,CAAC1C,IAAI,EADC0C,IAAI,CAAClB,EAEV,CACT,CAAC,GACA,CAAC,CACRjC,MAAM,CAACP,OAAO,eACbnC,KAAA,MAAGmF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC7EtF,IAAA,CAACR,WAAW,EAAC6F,SAAS,CAAC,4BAA4B,CAAE,CAAC,CACrDzC,MAAM,CAACP,OAAO,EACd,CACJ,CACA,CAACN,QAAQ,CAACK,aAAa,eACtBlC,KAAA,MAAGmF,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAC9EtF,IAAA,CAACT,KAAK,EAAC8F,SAAS,CAAC,4BAA4B,CAAE,CAAC,mCAElD,EAAG,CACJ,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNrF,IAAA,QAAKqF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCpF,KAAA,QACEmF,SAAS,CAAE,yEACTD,SAAS,CACL,4BAA4B,CAC5B,4BAA4B,EAC/B,CAAAE,QAAA,eAEHpF,KAAA,QAAKmF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDtF,IAAA,CAACZ,IAAI,EAACiG,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACxDrF,IAAA,OAAIqF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,iBAE/D,CAAI,CAAC,EACF,CAAC,cAENpF,KAAA,QAAKmF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DpF,KAAA,QAAAoF,QAAA,eACEpF,KAAA,UAAOmF,SAAS,CAAC,6EAA6E,CAAAC,QAAA,EAAC,iBAC9E,cAAAtF,IAAA,SAAMqF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EACjD,CAAC,cACRtF,IAAA,UACEmG,IAAI,CAAC,MAAM,CACX9C,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAEvB,QAAQ,CAACE,cAAe,CAC/BuD,QAAQ,CAAErC,iBAAkB,CAC5BsC,OAAO,CAAEA,CAAA,GAAMzC,eAAe,CAAC,gBAAgB,CAAE,CACjD0C,MAAM,CAAEA,CAAA,GAAM1C,eAAe,CAAC,IAAI,CAAE,CACpCoD,WAAW,CAAC,kCAAkC,CAC9Cf,SAAS,CAAE,8HACTzC,MAAM,CAACX,cAAc,CACjB,qCAAqC,CACrCc,YAAY,GAAK,gBAAgB,CACjC,oDAAoD,CACpD,4DAA4D,EAC/D,CACJ,CAAC,CACDH,MAAM,CAACX,cAAc,eACpB/B,KAAA,MAAGmF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC7EtF,IAAA,CAACR,WAAW,EAAC6F,SAAS,CAAC,4BAA4B,CAAE,CAAC,CACrDzC,MAAM,CAACX,cAAc,EACrB,CACJ,EACE,CAAC,cAEN/B,KAAA,QAAAoF,QAAA,eACEpF,KAAA,UAAOmF,SAAS,CAAC,6EAA6E,CAAAC,QAAA,EAAC,WACpF,cAAAtF,IAAA,SAAMqF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EAC3C,CAAC,cACRtF,IAAA,UACEmG,IAAI,CAAC,MAAM,CACX9C,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEvB,QAAQ,CAACG,QAAS,CACzBsD,QAAQ,CAAErC,iBAAkB,CAC5BsC,OAAO,CAAEA,CAAA,GAAMzC,eAAe,CAAC,UAAU,CAAE,CAC3C0C,MAAM,CAAEA,CAAA,GAAM1C,eAAe,CAAC,IAAI,CAAE,CACpCoD,WAAW,CAAC,yBAAyB,CACrCf,SAAS,CAAE,8HACTzC,MAAM,CAACV,QAAQ,CACX,qCAAqC,CACrCa,YAAY,GAAK,UAAU,CAC3B,oDAAoD,CACpD,4DAA4D,EAC/D,CACJ,CAAC,CACDH,MAAM,CAACV,QAAQ,eACdhC,KAAA,MAAGmF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC7EtF,IAAA,CAACR,WAAW,EAAC6F,SAAS,CAAC,4BAA4B,CAAE,CAAC,CACrDzC,MAAM,CAACV,QAAQ,EACf,CACJ,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNlC,IAAA,QAAKqF,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BpF,KAAA,QAAKmF,SAAS,CAAC,uEAAuE,CAAAC,QAAA,eACpFpF,KAAA,QAAKmF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDtF,IAAA,CAACX,IAAI,EAACgG,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACxDrF,IAAA,OAAIqF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,UAE/D,CAAI,CAAC,EACF,CAAC,cAENpF,KAAA,QAAAoF,QAAA,eACEpF,KAAA,UAAOmF,SAAS,CAAC,6EAA6E,CAAAC,QAAA,EAAC,WACpF,cAAAtF,IAAA,SAAMqF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EAC3C,CAAC,cACRpF,KAAA,QAAKmF,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBtF,IAAA,UACEmG,IAAI,CAAExD,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCU,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEvB,QAAQ,CAACI,QAAS,CACzBqD,QAAQ,CAAErC,iBAAkB,CAC5BsC,OAAO,CAAEA,CAAA,GAAMzC,eAAe,CAAC,UAAU,CAAE,CAC3C0C,MAAM,CAAEA,CAAA,GAAM1C,eAAe,CAAC,IAAI,CAAE,CACpCoD,WAAW,CAAC,gBAAgB,CAC5Bf,SAAS,CAAE,6IACTzC,MAAM,CAACT,QAAQ,CACX,qCAAqC,CACrCY,YAAY,GAAK,UAAU,CAC3B,oDAAoD,CACpD,gEAAgE,EACnE,CACJ,CAAC,cAGF7C,KAAA,QAAKmF,SAAS,CAAC,iGAAiG,CAAAC,QAAA,eAC9GpF,KAAA,WACEiG,IAAI,CAAC,QAAQ,CACbZ,OAAO,CAAEL,oBAAqB,CAC9BS,QAAQ,CAAE,CAACnF,iBAAkB,CAC7B6E,SAAS,CAAE,qIACT7E,iBAAiB,CACb,uDAAuD,CACvD,8CAA8C,EACjD,CACH8B,KAAK,CAAC,wBAAwB,CAAAgD,QAAA,eAE9BtF,IAAA,CAACN,KAAK,EAAC2F,SAAS,CAAC,2BAA2B,CAAE,CAAC,cAC/CrF,IAAA,SAAMqF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,EACvC,CAAC,cAETtF,IAAA,WACEmG,IAAI,CAAC,QAAQ,CACbZ,OAAO,CAAEJ,wBAAyB,CAClCE,SAAS,CAAC,4GAA4G,CAAAC,QAAA,CAErH3C,YAAY,cACX3C,IAAA,CAACf,MAAM,EAACoG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAE5CrF,IAAA,CAAChB,GAAG,EAACqG,SAAS,CAAC,uBAAuB,CAAE,CACzC,CACK,CAAC,EACN,CAAC,EACH,CAAC,CAELzC,MAAM,CAACT,QAAQ,eACdjC,KAAA,MAAGmF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC7EtF,IAAA,CAACR,WAAW,EAAC6F,SAAS,CAAC,4BAA4B,CAAE,CAAC,CACrDzC,MAAM,CAACT,QAAQ,EACf,CACJ,CAGAJ,QAAQ,CAACI,QAAQ,eAChBjC,KAAA,QAAKmF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpF,KAAA,QAAKmF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDtF,IAAA,SAAMqF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,mBAE/D,CAAM,CAAC,cACPtF,IAAA,SACEqF,SAAS,CAAE,yDAAyDzB,gBAAgB,CAClF7B,QAAQ,CAACmB,QACX,CAAC,EAAG,CAAAoC,QAAA,CAEHvD,QAAQ,CAACmB,QAAQ,CACd,CAAC,EACJ,CAAC,cACNlD,IAAA,QAAKqF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,cAC3DtF,IAAA,QACEqF,SAAS,CAAE,yDACTxB,mBAAmB,CAAC9B,QAAQ,CAACmB,QAAQ,CAAC,CAACa,KAAK,EAC3C,CACHsC,KAAK,CAAE,CACLvC,KAAK,CAAED,mBAAmB,CAAC9B,QAAQ,CAACmB,QAAQ,CAAC,CAACY,KAChD,CAAE,CACH,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN5D,KAAA,QAAKmF,SAAS,CAAC,oJAAoJ,CAAAC,QAAA,eACjKpF,KAAA,QAAKmF,SAAS,CAAC,8DAA8D,CAAAC,QAAA,eAC3EtF,IAAA,CAACP,WAAW,EAAC4F,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACjDrF,IAAA,SAAAsF,QAAA,CAAM,uCAAqC,CAAM,CAAC,EAC/C,CAAC,cAGNpF,KAAA,QAAKmF,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxEtF,IAAA,WACEmG,IAAI,CAAC,QAAQ,CACbZ,OAAO,CAAEhF,QAAS,CAClB8E,SAAS,CAAC,mQAAmQ,CAAAC,QAAA,CAC9Q,QAED,CAAQ,CAAC,cACTtF,IAAA,WACEmG,IAAI,CAAC,QAAQ,CACbZ,OAAO,CAAElB,YAAa,CACtBsB,QAAQ,CAAEzE,SAAS,EAAIU,UAAU,EAAIE,UAAW,CAChDuD,SAAS,CAAC,qVAAqV,CAAAC,QAAA,CAE9VpE,SAAS,EAAIU,UAAU,EAAIE,UAAU,cACpC5B,KAAA,CAAAE,SAAA,EAAAkF,QAAA,eACEtF,IAAA,QAAKqF,SAAS,CAAC,4FAA4F,CAAM,CAAC,cAClHrF,IAAA,SAAMqF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC/B5E,QAAQ,CAAG,aAAa,CAAG,WAAW,CACnC,CAAC,cACPV,IAAA,SAAMqF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACxB5E,QAAQ,CAAG,QAAQ,CAAG,MAAM,CACzB,CAAC,EACP,CAAC,cAEHR,KAAA,CAAAE,SAAA,EAAAkF,QAAA,eACEtF,IAAA,CAACb,MAAM,EAACkG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC5CrF,IAAA,SAAMqF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC/B5E,QAAQ,CAAG,iBAAiB,CAAG,eAAe,CAC3C,CAAC,cACPV,IAAA,SAAMqF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACxB5E,QAAQ,CAAG,QAAQ,CAAG,MAAM,CACzB,CAAC,EACP,CACH,CACK,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}